// Generated by view binder compiler. Do not edit!
package com.motasem.visualpointer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ScrollView;
import android.widget.SeekBar;
import android.widget.Switch;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.motasem.visualpointer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivitySettingsBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button backButton;

  @NonNull
  public final SeekBar blinkDurationSeekBar;

  @NonNull
  public final SeekBar blinkSensitivitySeekBar;

  @NonNull
  public final SeekBar cursorSizeSeekBar;

  @NonNull
  public final SeekBar cursorSpeedSeekBar;

  @NonNull
  public final SeekBar eyeTrackingSensitivitySeekBar;

  @NonNull
  public final Button resetSettingsButton;

  @NonNull
  public final Button saveSettingsButton;

  @NonNull
  public final Switch showFaceOutlineSwitch;

  private ActivitySettingsBinding(@NonNull ScrollView rootView, @NonNull Button backButton,
      @NonNull SeekBar blinkDurationSeekBar, @NonNull SeekBar blinkSensitivitySeekBar,
      @NonNull SeekBar cursorSizeSeekBar, @NonNull SeekBar cursorSpeedSeekBar,
      @NonNull SeekBar eyeTrackingSensitivitySeekBar, @NonNull Button resetSettingsButton,
      @NonNull Button saveSettingsButton, @NonNull Switch showFaceOutlineSwitch) {
    this.rootView = rootView;
    this.backButton = backButton;
    this.blinkDurationSeekBar = blinkDurationSeekBar;
    this.blinkSensitivitySeekBar = blinkSensitivitySeekBar;
    this.cursorSizeSeekBar = cursorSizeSeekBar;
    this.cursorSpeedSeekBar = cursorSpeedSeekBar;
    this.eyeTrackingSensitivitySeekBar = eyeTrackingSensitivitySeekBar;
    this.resetSettingsButton = resetSettingsButton;
    this.saveSettingsButton = saveSettingsButton;
    this.showFaceOutlineSwitch = showFaceOutlineSwitch;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.backButton;
      Button backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.blinkDurationSeekBar;
      SeekBar blinkDurationSeekBar = ViewBindings.findChildViewById(rootView, id);
      if (blinkDurationSeekBar == null) {
        break missingId;
      }

      id = R.id.blinkSensitivitySeekBar;
      SeekBar blinkSensitivitySeekBar = ViewBindings.findChildViewById(rootView, id);
      if (blinkSensitivitySeekBar == null) {
        break missingId;
      }

      id = R.id.cursorSizeSeekBar;
      SeekBar cursorSizeSeekBar = ViewBindings.findChildViewById(rootView, id);
      if (cursorSizeSeekBar == null) {
        break missingId;
      }

      id = R.id.cursorSpeedSeekBar;
      SeekBar cursorSpeedSeekBar = ViewBindings.findChildViewById(rootView, id);
      if (cursorSpeedSeekBar == null) {
        break missingId;
      }

      id = R.id.eyeTrackingSensitivitySeekBar;
      SeekBar eyeTrackingSensitivitySeekBar = ViewBindings.findChildViewById(rootView, id);
      if (eyeTrackingSensitivitySeekBar == null) {
        break missingId;
      }

      id = R.id.resetSettingsButton;
      Button resetSettingsButton = ViewBindings.findChildViewById(rootView, id);
      if (resetSettingsButton == null) {
        break missingId;
      }

      id = R.id.saveSettingsButton;
      Button saveSettingsButton = ViewBindings.findChildViewById(rootView, id);
      if (saveSettingsButton == null) {
        break missingId;
      }

      id = R.id.showFaceOutlineSwitch;
      Switch showFaceOutlineSwitch = ViewBindings.findChildViewById(rootView, id);
      if (showFaceOutlineSwitch == null) {
        break missingId;
      }

      return new ActivitySettingsBinding((ScrollView) rootView, backButton, blinkDurationSeekBar,
          blinkSensitivitySeekBar, cursorSizeSeekBar, cursorSpeedSeekBar,
          eyeTrackingSensitivitySeekBar, resetSettingsButton, saveSettingsButton,
          showFaceOutlineSwitch);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
