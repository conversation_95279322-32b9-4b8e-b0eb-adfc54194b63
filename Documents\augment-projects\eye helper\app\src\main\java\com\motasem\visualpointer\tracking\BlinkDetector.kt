package com.motasem.visualpointer.tracking

import android.util.Log
import com.motasem.visualpointer.model.BlinkEvent
import com.motasem.visualpointer.model.BlinkEye
import com.motasem.visualpointer.utils.PreferencesManager

/**
 * Advanced blink detection and gesture recognition
 */
class BlinkDetector(private val preferencesManager: PreferencesManager) {

    companion object {
        private const val TAG = "BlinkDetector"
        private const val MIN_BLINK_DURATION = 100L // Minimum duration to consider as intentional blink
        private const val MAX_BLINK_DURATION = 1000L // Maximum duration for a single blink
        private const val DOUBLE_BLINK_WINDOW = 500L // Time window for detecting double blinks
    }

    private var leftEyeState = EyeState()
    private var rightEyeState = EyeState()
    private var lastBlinkEvent: BlinkEvent? = null
    
    private val blinkListeners = mutableListOf<(BlinkEvent) -> Unit>()

    data class EyeState(
        var isOpen: Boolean = true,
        var lastStateChange: Long = 0L,
        var blinkStartTime: Long = 0L
    )

    /**
     * Process eye open probabilities and detect blinks
     */
    fun processEyeStates(leftEyeProb: Float, rightEyeProb: Float) {
        val currentTime = System.currentTimeMillis()
        val blinkThreshold = preferencesManager.getBlinkThreshold()
        
        // Determine current eye states
        val leftOpen = leftEyeProb > blinkThreshold
        val rightOpen = rightEyeProb > blinkThreshold
        
        // Process left eye
        processEyeState(leftEyeState, leftOpen, currentTime, BlinkEye.LEFT)
        
        // Process right eye
        processEyeState(rightEyeState, rightOpen, currentTime, BlinkEye.RIGHT)
        
        // Check for both eyes closed
        if (!leftOpen && !rightOpen) {
            processBothEyesClosed(currentTime)
        }
    }

    /**
     * Process individual eye state changes
     */
    private fun processEyeState(eyeState: EyeState, isOpen: Boolean, currentTime: Long, eye: BlinkEye) {
        if (eyeState.isOpen != isOpen) {
            // State changed
            if (!isOpen) {
                // Eye just closed
                eyeState.blinkStartTime = currentTime
                Log.d(TAG, "${eye.name} eye closed")
            } else {
                // Eye just opened
                val blinkDuration = currentTime - eyeState.blinkStartTime
                if (isValidBlink(blinkDuration)) {
                    val blinkEvent = BlinkEvent(eye, blinkDuration, currentTime)
                    onBlinkDetected(blinkEvent)
                    Log.d(TAG, "${eye.name} eye blink detected: ${blinkDuration}ms")
                }
            }
            
            eyeState.isOpen = isOpen
            eyeState.lastStateChange = currentTime
        }
    }

    /**
     * Process both eyes closed state
     */
    private fun processBothEyesClosed(currentTime: Long) {
        // Check if both eyes have been closed for a sufficient duration
        val leftClosedDuration = if (!leftEyeState.isOpen) currentTime - leftEyeState.blinkStartTime else 0L
        val rightClosedDuration = if (!rightEyeState.isOpen) currentTime - rightEyeState.blinkStartTime else 0L
        
        val minDuration = minOf(leftClosedDuration, rightClosedDuration)
        
        if (minDuration > preferencesManager.getBlinkDurationThreshold()) {
            // Both eyes have been closed long enough
            val blinkEvent = BlinkEvent(BlinkEye.BOTH, minDuration, currentTime)
            
            // Avoid duplicate events
            if (lastBlinkEvent?.eye != BlinkEye.BOTH || 
                currentTime - (lastBlinkEvent?.timestamp ?: 0L) > 1000L) {
                onBlinkDetected(blinkEvent)
                Log.d(TAG, "Both eyes blink detected: ${minDuration}ms")
            }
        }
    }

    /**
     * Check if blink duration is valid for intentional gesture
     */
    private fun isValidBlink(duration: Long): Boolean {
        val minDuration = preferencesManager.getBlinkDurationThreshold()
        return duration in minDuration..MAX_BLINK_DURATION
    }

    /**
     * Handle detected blink event
     */
    private fun onBlinkDetected(blinkEvent: BlinkEvent) {
        lastBlinkEvent = blinkEvent
        
        // Notify all listeners
        blinkListeners.forEach { listener ->
            try {
                listener(blinkEvent)
            } catch (e: Exception) {
                Log.e(TAG, "Error in blink listener", e)
            }
        }
    }

    /**
     * Add blink event listener
     */
    fun addBlinkListener(listener: (BlinkEvent) -> Unit) {
        blinkListeners.add(listener)
    }

    /**
     * Remove blink event listener
     */
    fun removeBlinkListener(listener: (BlinkEvent) -> Unit) {
        blinkListeners.remove(listener)
    }

    /**
     * Clear all listeners
     */
    fun clearListeners() {
        blinkListeners.clear()
    }

    /**
     * Reset detector state
     */
    fun reset() {
        leftEyeState = EyeState()
        rightEyeState = EyeState()
        lastBlinkEvent = null
    }

    /**
     * Get current eye states for debugging
     */
    fun getCurrentStates(): Pair<Boolean, Boolean> {
        return Pair(leftEyeState.isOpen, rightEyeState.isOpen)
    }

    /**
     * Check if eyes are currently stable (not blinking frequently)
     */
    fun areEyesStable(): Boolean {
        val currentTime = System.currentTimeMillis()
        val stableThreshold = 2000L // 2 seconds
        
        return (currentTime - leftEyeState.lastStateChange > stableThreshold) &&
               (currentTime - rightEyeState.lastStateChange > stableThreshold)
    }

    /**
     * Get time since last blink
     */
    fun getTimeSinceLastBlink(): Long {
        return System.currentTimeMillis() - (lastBlinkEvent?.timestamp ?: 0L)
    }
}
