<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Title -->
        <TextView
            style="@style/TitleText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/settings"
            android:textAlignment="center" />

        <!-- Sensitivity Settings Card -->
        <androidx.cardview.widget.CardView
            style="@style/CardStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    style="@style/SubtitleText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/sensitivity_settings"
                    android:textStyle="bold" />

                <!-- Eye Tracking Sensitivity -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/eye_tracking_sensitivity"
                    android:textColor="@color/text_primary" />

                <SeekBar
                    android:id="@+id/eyeTrackingSensitivitySeekBar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:max="100"
                    android:progress="50" />

                <!-- Blink Detection Sensitivity -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/blink_detection_sensitivity"
                    android:textColor="@color/text_primary" />

                <SeekBar
                    android:id="@+id/blinkSensitivitySeekBar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:max="100"
                    android:progress="60" />

                <!-- Cursor Speed -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/cursor_speed"
                    android:textColor="@color/text_primary" />

                <SeekBar
                    android:id="@+id/cursorSpeedSeekBar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:max="100"
                    android:progress="70" />

                <!-- Blink Duration -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/blink_duration"
                    android:textColor="@color/text_primary" />

                <SeekBar
                    android:id="@+id/blinkDurationSeekBar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:max="100"
                    android:progress="40" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Visual Settings Card -->
        <androidx.cardview.widget.CardView
            style="@style/CardStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    style="@style/SubtitleText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/visual_settings"
                    android:textStyle="bold" />

                <!-- Cursor Size -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/cursor_size"
                    android:textColor="@color/text_primary" />

                <SeekBar
                    android:id="@+id/cursorSizeSeekBar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:max="100"
                    android:progress="50" />

                <!-- Show Face Outline -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/show_face_outline"
                        android:textColor="@color/text_primary" />

                    <Switch
                        android:id="@+id/showFaceOutlineSwitch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:orientation="vertical">

            <Button
                android:id="@+id/saveSettingsButton"
                style="@style/PrimaryButton"
                android:layout_width="match_parent"
                android:text="حفظ الإعدادات" />

            <Button
                android:id="@+id/resetSettingsButton"
                style="@style/SecondaryButton"
                android:layout_width="match_parent"
                android:text="إعادة تعيين" />

            <Button
                android:id="@+id/backButton"
                style="@style/SecondaryButton"
                android:layout_width="match_parent"
                android:text="رجوع" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
