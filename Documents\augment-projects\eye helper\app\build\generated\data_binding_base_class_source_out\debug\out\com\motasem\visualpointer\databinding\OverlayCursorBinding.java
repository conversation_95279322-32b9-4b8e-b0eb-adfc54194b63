// Generated by view binder compiler. Do not edit!
package com.motasem.visualpointer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.motasem.visualpointer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class OverlayCursorBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final ImageView cursorView;

  @NonNull
  public final View pulseRing;

  private OverlayCursorBinding(@NonNull FrameLayout rootView, @NonNull ImageView cursorView,
      @NonNull View pulseRing) {
    this.rootView = rootView;
    this.cursorView = cursorView;
    this.pulseRing = pulseRing;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static OverlayCursorBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static OverlayCursorBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.overlay_cursor, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static OverlayCursorBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cursorView;
      ImageView cursorView = ViewBindings.findChildViewById(rootView, id);
      if (cursorView == null) {
        break missingId;
      }

      id = R.id.pulseRing;
      View pulseRing = ViewBindings.findChildViewById(rootView, id);
      if (pulseRing == null) {
        break missingId;
      }

      return new OverlayCursorBinding((FrameLayout) rootView, cursorView, pulseRing);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
