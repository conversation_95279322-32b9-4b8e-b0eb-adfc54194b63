<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/context_menu_item_background"
    android:clickable="true"
    android:focusable="true"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:padding="12dp">

    <View
        android:id="@+id/menuItemIcon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="12dp" />

    <TextView
        android:id="@+id/menuItemTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="Menu Item"
        android:textColor="@color/text_primary"
        android:textSize="16sp" />

</LinearLayout>
