R_DEF: Internal format may change without notice
local
color accent
color accent_dark
color background
color black
color card_background
color cursor_active
color cursor_default
color cursor_inactive
color error
color eye_closed
color eye_open
color face_outline
color ic_launcher_background
color info
color overlay_background
color primary
color primary_dark
color primary_light
color semi_transparent
color success
color surface
color text_hint
color text_primary
color text_secondary
color text_white
color transparent
color warning
color white
drawable calibration_point
drawable context_menu_background
drawable context_menu_item_background
drawable cursor_circle
drawable cursor_pulse_ring
drawable ic_back
drawable ic_click
drawable ic_eye
drawable ic_home
drawable ic_launcher_foreground
drawable ic_launcher_simple
drawable ic_long_click
drawable ic_recent
drawable ic_scroll_down
drawable ic_scroll_up
drawable splash_background
drawable status_background
id aboutButton
id appVersionText
id backButton
id backFromCalibrationButton
id blinkDurationSeekBar
id blinkSensitivitySeekBar
id buttonContainer
id calibrationButton
id calibrationGraphicOverlay
id calibrationInstruction
id calibrationPoint
id calibrationPointsContainer
id calibrationPreviewView
id calibrationProgress
id calibrationProgressBar
id calibrationTitle
id cameraContainer
id controlButtonsContainer
id cursorSizeSeekBar
id cursorSpeedSeekBar
id cursorView
id emailContactCard
id eyeTrackingSensitivitySeekBar
id githubLinkCard
id graphicOverlay
id helpButton
id instructionsContainer
id instructionsOverlay
id menuItemIcon
id menuItemTitle
id previewView
id privacyPolicyCard
id pulseRing
id rateAppCard
id resetSettingsButton
id saveSettingsButton
id settingsButton
id shareAppCard
id showFaceOutlineSwitch
id skipCalibrationButton
id startCalibrationButton
id startTrackingButton
id statusContainer
id statusText
id subtitleText
id titleContainer
id titleText
id toolbar
id whatsappContactCard
layout activity_about
layout activity_calibration
layout activity_main
layout activity_settings
layout context_menu_item
layout overlay_cursor
mipmap ic_launcher
mipmap ic_launcher_round
string about
string about_description
string about_title
string accessibility_permission_message
string accessibility_permission_title
string accessibility_service_description
string action_back
string action_click
string action_home
string action_long_click
string action_recent
string action_scroll_down
string action_scroll_up
string app_name
string app_name_en
string blink_detection_sensitivity
string blink_duration
string both_eyes_closed
string calibration
string calibration_complete
string calibration_failed
string calibration_instruction
string calibration_point
string calibration_title
string camera_permission_message
string camera_permission_title
string cancel
string contact_us
string copyright
string cursor_color
string cursor_size
string cursor_speed
string developer_experience
string developer_info
string developer_name
string developer_specialty
string email_contact
string error_camera_not_available
string error_face_detection_failed
string error_permission_denied
string error_service_not_running
string eye_tracking_sensitivity
string face_detected
string github_link
string grant_permission
string help
string instruction_both_blink
string instruction_left_blink
string instruction_right_blink
string left_eye_closed
string made_with_love
string multiple_faces_detected
string no_face_detected
string ok
string overlay_permission_message
string overlay_permission_title
string privacy_policy
string rate_app
string right_eye_closed
string sensitivity_settings
string settings
string share_app
string show_face_outline
string start_tracking
string tracking_active
string useful_links
string visual_settings
string welcome_subtitle
string welcome_title
string whatsapp_contact
style CardStyle
style PrimaryButton
style SecondaryButton
style StatusText
style SubtitleText
style Theme.VisualPointer
style Theme.VisualPointer.Fullscreen
style Theme.VisualPointer.Splash
style TitleText
xml accessibility_service_config
xml backup_rules
xml data_extraction_rules
