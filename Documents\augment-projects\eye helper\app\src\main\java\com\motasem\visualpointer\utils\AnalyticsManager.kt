package com.motasem.visualpointer.utils

import android.content.Context
import android.util.Log
import com.motasem.visualpointer.gesture.GestureManager
import com.motasem.visualpointer.model.BlinkEye
import com.motasem.visualpointer.model.TrackingStatus
import java.text.SimpleDateFormat
import java.util.*

/**
 * Manager for tracking app usage analytics and statistics
 */
class AnalyticsManager(private val context: Context) {

    companion object {
        private const val TAG = "AnalyticsManager"
        private const val PREFS_NAME = "analytics_prefs"
        
        // Keys for analytics data
        private const val KEY_SESSION_COUNT = "session_count"
        private const val KEY_TOTAL_USAGE_TIME = "total_usage_time"
        private const val KEY_GESTURE_COUNTS = "gesture_counts"
        private const val KEY_BLINK_COUNTS = "blink_counts"
        private const val KEY_ACCURACY_DATA = "accuracy_data"
        private const val KEY_LAST_SESSION_DATE = "last_session_date"
    }

    private val sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private var sessionStartTime = 0L
    private var currentSessionGestures = mutableMapOf<GestureManager.GestureType, Int>()
    private var currentSessionBlinks = mutableMapOf<BlinkEye, Int>()
    private var trackingStatusHistory = mutableListOf<TrackingStatus>()

    /**
     * Start a new tracking session
     */
    fun startSession() {
        sessionStartTime = System.currentTimeMillis()
        currentSessionGestures.clear()
        currentSessionBlinks.clear()
        trackingStatusHistory.clear()
        
        // Increment session count
        val sessionCount = sharedPreferences.getInt(KEY_SESSION_COUNT, 0) + 1
        sharedPreferences.edit().putInt(KEY_SESSION_COUNT, sessionCount).apply()
        
        // Update last session date
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        val currentDate = dateFormat.format(Date())
        sharedPreferences.edit().putString(KEY_LAST_SESSION_DATE, currentDate).apply()
        
        Log.d(TAG, "Session started: #$sessionCount")
    }

    /**
     * End the current tracking session
     */
    fun endSession() {
        if (sessionStartTime == 0L) return
        
        val sessionDuration = System.currentTimeMillis() - sessionStartTime
        
        // Update total usage time
        val totalUsageTime = sharedPreferences.getLong(KEY_TOTAL_USAGE_TIME, 0L) + sessionDuration
        sharedPreferences.edit().putLong(KEY_TOTAL_USAGE_TIME, totalUsageTime).apply()
        
        // Save gesture counts
        saveGestureCounts()
        
        // Save blink counts
        saveBlinkCounts()
        
        // Calculate and save accuracy data
        calculateAndSaveAccuracy()
        
        Log.d(TAG, "Session ended. Duration: ${sessionDuration / 1000}s")
        
        sessionStartTime = 0L
    }

    /**
     * Record a gesture execution
     */
    fun recordGesture(gestureType: GestureManager.GestureType) {
        currentSessionGestures[gestureType] = currentSessionGestures.getOrDefault(gestureType, 0) + 1
        Log.d(TAG, "Gesture recorded: $gestureType")
    }

    /**
     * Record a blink detection
     */
    fun recordBlink(blinkEye: BlinkEye) {
        currentSessionBlinks[blinkEye] = currentSessionBlinks.getOrDefault(blinkEye, 0) + 1
        Log.d(TAG, "Blink recorded: $blinkEye")
    }

    /**
     * Record tracking status for accuracy calculation
     */
    fun recordTrackingStatus(status: TrackingStatus) {
        trackingStatusHistory.add(status)
        
        // Keep only last 100 status updates for performance
        if (trackingStatusHistory.size > 100) {
            trackingStatusHistory.removeAt(0)
        }
    }

    /**
     * Save gesture counts to preferences
     */
    private fun saveGestureCounts() {
        val existingCounts = getGestureCounts().toMutableMap()
        
        currentSessionGestures.forEach { (gesture, count) ->
            existingCounts[gesture] = existingCounts.getOrDefault(gesture, 0) + count
        }
        
        val countsString = existingCounts.map { "${it.key.name}:${it.value}" }.joinToString(",")
        sharedPreferences.edit().putString(KEY_GESTURE_COUNTS, countsString).apply()
    }

    /**
     * Save blink counts to preferences
     */
    private fun saveBlinkCounts() {
        val existingCounts = getBlinkCounts().toMutableMap()
        
        currentSessionBlinks.forEach { (eye, count) ->
            existingCounts[eye] = existingCounts.getOrDefault(eye, 0) + count
        }
        
        val countsString = existingCounts.map { "${it.key.name}:${it.value}" }.joinToString(",")
        sharedPreferences.edit().putString(KEY_BLINK_COUNTS, countsString).apply()
    }

    /**
     * Calculate and save accuracy data
     */
    private fun calculateAndSaveAccuracy() {
        if (trackingStatusHistory.isEmpty()) return
        
        val totalStatusUpdates = trackingStatusHistory.size
        val successfulTracking = trackingStatusHistory.count { 
            it == TrackingStatus.FACE_DETECTED || it == TrackingStatus.TRACKING_ACTIVE 
        }
        
        val accuracy = (successfulTracking.toFloat() / totalStatusUpdates) * 100
        
        // Save accuracy data (simplified - in real app you might want more sophisticated tracking)
        sharedPreferences.edit().putFloat(KEY_ACCURACY_DATA, accuracy).apply()
        
        Log.d(TAG, "Session accuracy: ${String.format("%.1f", accuracy)}%")
    }

    /**
     * Get total session count
     */
    fun getSessionCount(): Int {
        return sharedPreferences.getInt(KEY_SESSION_COUNT, 0)
    }

    /**
     * Get total usage time in milliseconds
     */
    fun getTotalUsageTime(): Long {
        return sharedPreferences.getLong(KEY_TOTAL_USAGE_TIME, 0L)
    }

    /**
     * Get gesture counts
     */
    fun getGestureCounts(): Map<GestureManager.GestureType, Int> {
        val countsString = sharedPreferences.getString(KEY_GESTURE_COUNTS, "") ?: ""
        val counts = mutableMapOf<GestureManager.GestureType, Int>()
        
        if (countsString.isNotEmpty()) {
            countsString.split(",").forEach { entry ->
                val parts = entry.split(":")
                if (parts.size == 2) {
                    try {
                        val gestureType = GestureManager.GestureType.valueOf(parts[0])
                        val count = parts[1].toInt()
                        counts[gestureType] = count
                    } catch (e: Exception) {
                        Log.w(TAG, "Error parsing gesture count: $entry", e)
                    }
                }
            }
        }
        
        return counts
    }

    /**
     * Get blink counts
     */
    fun getBlinkCounts(): Map<BlinkEye, Int> {
        val countsString = sharedPreferences.getString(KEY_BLINK_COUNTS, "") ?: ""
        val counts = mutableMapOf<BlinkEye, Int>()
        
        if (countsString.isNotEmpty()) {
            countsString.split(",").forEach { entry ->
                val parts = entry.split(":")
                if (parts.size == 2) {
                    try {
                        val blinkEye = BlinkEye.valueOf(parts[0])
                        val count = parts[1].toInt()
                        counts[blinkEye] = count
                    } catch (e: Exception) {
                        Log.w(TAG, "Error parsing blink count: $entry", e)
                    }
                }
            }
        }
        
        return counts
    }

    /**
     * Get current accuracy percentage
     */
    fun getAccuracy(): Float {
        return sharedPreferences.getFloat(KEY_ACCURACY_DATA, 0f)
    }

    /**
     * Get last session date
     */
    fun getLastSessionDate(): String? {
        return sharedPreferences.getString(KEY_LAST_SESSION_DATE, null)
    }

    /**
     * Get formatted usage statistics
     */
    fun getUsageStatistics(): String {
        val sessionCount = getSessionCount()
        val totalTime = getTotalUsageTime()
        val accuracy = getAccuracy()
        val lastSession = getLastSessionDate()
        
        val hours = totalTime / (1000 * 60 * 60)
        val minutes = (totalTime % (1000 * 60 * 60)) / (1000 * 60)
        
        return """
            إحصائيات الاستخدام:
            
            عدد الجلسات: $sessionCount
            إجمالي وقت الاستخدام: ${hours}س ${minutes}د
            دقة التتبع: ${String.format("%.1f", accuracy)}%
            آخر جلسة: ${lastSession ?: "غير متوفر"}
            
            الإيماءات المستخدمة:
            ${getGestureCountsFormatted()}
            
            الغمزات المكتشفة:
            ${getBlinkCountsFormatted()}
        """.trimIndent()
    }

    private fun getGestureCountsFormatted(): String {
        val gestureCounts = getGestureCounts()
        return if (gestureCounts.isEmpty()) {
            "لا توجد بيانات"
        } else {
            gestureCounts.map { "${it.key.name}: ${it.value}" }.joinToString("\n")
        }
    }

    private fun getBlinkCountsFormatted(): String {
        val blinkCounts = getBlinkCounts()
        return if (blinkCounts.isEmpty()) {
            "لا توجد بيانات"
        } else {
            blinkCounts.map { "${it.key.name}: ${it.value}" }.joinToString("\n")
        }
    }

    /**
     * Reset all analytics data
     */
    fun resetAnalytics() {
        sharedPreferences.edit().clear().apply()
        Log.d(TAG, "Analytics data reset")
    }
}
