<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".MainActivity">

    <!-- Camera Preview Container -->
    <FrameLayout
        android:id="@+id/cameraContainer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintBottom_toTopOf="@+id/statusContainer"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titleContainer">

        <!-- Camera Preview -->
        <androidx.camera.view.PreviewView
            android:id="@+id/previewView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="8dp"
            app:scaleType="fillCenter" />

        <!-- Graphics Overlay for face detection -->
        <com.motasem.visualpointer.ui.GraphicOverlay
            android:id="@+id/graphicOverlay"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="8dp" />

        <!-- Instructions Overlay -->
        <LinearLayout
            android:id="@+id/instructionsOverlay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="top"
            android:layout_margin="16dp"
            android:background="@drawable/status_background"
            android:orientation="vertical"
            android:padding="12dp"
            android:visibility="visible">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/instruction_right_blink"
                android:textColor="@color/white"
                android:textSize="12sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/instruction_left_blink"
                android:textColor="@color/white"
                android:textSize="12sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/instruction_both_blink"
                android:textColor="@color/white"
                android:textSize="12sp" />

        </LinearLayout>

    </FrameLayout>

    <!-- Title Container -->
    <LinearLayout
        android:id="@+id/titleContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/titleText"
            style="@style/TitleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/welcome_title"
            android:textAlignment="center" />

        <TextView
            android:id="@+id/subtitleText"
            style="@style/SubtitleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/welcome_subtitle"
            android:textAlignment="center" />

    </LinearLayout>

    <!-- Status Container -->
    <LinearLayout
        android:id="@+id/statusContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@+id/buttonContainer"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/statusText"
            style="@style/StatusText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/no_face_detected"
            android:textAlignment="center" />

    </LinearLayout>

    <!-- Button Container -->
    <LinearLayout
        android:id="@+id/buttonContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <Button
            android:id="@+id/startTrackingButton"
            style="@style/PrimaryButton"
            android:layout_width="match_parent"
            android:text="@string/start_tracking" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/settingsButton"
                style="@style/SecondaryButton"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:text="@string/settings" />

            <Button
                android:id="@+id/calibrationButton"
                style="@style/SecondaryButton"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:text="@string/calibration" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/helpButton"
                style="@style/SecondaryButton"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:text="@string/help" />

            <Button
                android:id="@+id/aboutButton"
                style="@style/SecondaryButton"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:text="حول البرنامج" />

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
