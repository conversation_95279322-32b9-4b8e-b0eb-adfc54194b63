package com.motasem.visualpointer

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.google.mlkit.vision.face.Face
import com.motasem.visualpointer.camera.CameraManager
import com.motasem.visualpointer.databinding.ActivityMainBinding
import com.motasem.visualpointer.model.TrackingStatus
import com.motasem.visualpointer.service.EyeTrackingService
import com.motasem.visualpointer.ui.CalibrationActivity
import com.motasem.visualpointer.ui.SettingsActivity
import com.motasem.visualpointer.utils.AnalyticsManager
import com.motasem.visualpointer.utils.PermissionHelper

class MainActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "MainActivity"
    }

    private lateinit var binding: ActivityMainBinding
    private lateinit var cameraManager: CameraManager
    private lateinit var analyticsManager: AnalyticsManager
    private var isTrackingActive = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        analyticsManager = AnalyticsManager(this)
        setupUI()
        createCameraManager()
        checkPermissions()
    }

    override fun onResume() {
        super.onResume()
        // إعادة فحص الصلاحيات عند العودة للتطبيق
        if (!isTrackingActive) {
            checkPermissions()
        }
    }

    override fun onPause() {
        super.onPause()
        // لا نوقف الكاميرا إذا كان التتبع نشط
        if (::cameraManager.isInitialized && !isTrackingActive) {
            cameraManager.stopCamera()
        }

        // إظهار نصائح للحفاظ على التطبيق نشط في الخلفية
        if (isTrackingActive) {
            showBackgroundRunningTips()
        }
    }

    private fun setupUI() {
        binding.startTrackingButton.setOnClickListener {
            if (isTrackingActive) {
                stopTracking()
            } else {
                startTracking()
            }
        }

        binding.settingsButton.setOnClickListener {
            startActivity(Intent(this, SettingsActivity::class.java))
        }

        binding.calibrationButton.setOnClickListener {
            startActivity(Intent(this, CalibrationActivity::class.java))
        }

        // إضافة زر لإعدادات الحساسية
        binding.settingsButton.setOnClickListener {
            showSensitivityDialog()
        }

        binding.helpButton.setOnClickListener {
            showHelpDialog()
        }

        // إضافة زر تشخيص مخفي (اضغط مطولاً على العنوان)
        binding.titleText.setOnLongClickListener {
            showDiagnosticDialog()
            true
        }
    }

    private fun createCameraManager() {
        cameraManager = CameraManager(
            this,
            binding.previewView,
            this,
            binding.graphicOverlay,
            ::onTrackingStatusChanged
        )
    }

    private fun checkPermissions() {
        Log.d(TAG, "Checking permissions...")

        val hasCameraPermission = PermissionHelper.hasCameraPermission(this)
        val hasOverlayPermission = PermissionHelper.hasOverlayPermission(this)
        val hasAccessibilityPermission = PermissionHelper.isAccessibilityServiceEnabled(this)

        Log.d(TAG, "Camera permission: $hasCameraPermission")
        Log.d(TAG, "Overlay permission: $hasOverlayPermission")
        Log.d(TAG, "Accessibility permission: $hasAccessibilityPermission")

        when {
            !hasCameraPermission -> {
                Log.d(TAG, "Requesting camera permission")
                requestCameraPermission()
            }
            !hasOverlayPermission -> {
                Log.d(TAG, "Requesting overlay permission")
                requestOverlayPermission()
            }
            !hasAccessibilityPermission -> {
                Log.d(TAG, "Requesting accessibility permission")
                requestAccessibilityPermission()
            }
            else -> {
                Log.d(TAG, "All permissions granted, enabling camera preview")
                enableCameraPreview()
            }
        }
    }

    private fun requestAccessibilityPermission() {
        showPermissionDialog(
            getString(R.string.accessibility_permission_title),
            getString(R.string.accessibility_permission_message) +
            "\n\nللأجهزة Realme:\n" +
            "الإعدادات → إعدادات إضافية → إمكانية الوصول → الخدمات\n" +
            "ابحث عن 'المؤشر البصري' وفعّله"
        ) {
            PermissionHelper.requestAccessibilityPermission(this)
        }
    }

    private fun requestCameraPermission() {
        when {
            PermissionHelper.shouldShowCameraPermissionRationale(this) -> {
                showPermissionDialog(
                    getString(R.string.camera_permission_title),
                    getString(R.string.camera_permission_message)
                ) {
                    cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
                }
            }
            else -> {
                cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
            }
        }
    }

    private fun requestOverlayPermission() {
        showPermissionDialog(
            getString(R.string.overlay_permission_title),
            getString(R.string.overlay_permission_message)
        ) {
            PermissionHelper.requestOverlayPermission(this)
        }
    }

    private val cameraPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            checkPermissions()
        } else {
            showPermissionDeniedDialog()
        }
    }

    private val overlayPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        if (PermissionHelper.hasOverlayPermission(this)) {
            checkPermissions() // Check next permission
        } else {
            showPermissionDeniedDialog()
        }
    }

    private fun showPermissionDialog(title: String, message: String, onPositive: () -> Unit) {
        AlertDialog.Builder(this)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton(getString(R.string.grant_permission)) { _, _ -> onPositive() }
            .setNegativeButton(getString(R.string.cancel)) { dialog, _ -> dialog.dismiss() }
            .setCancelable(false)
            .show()
    }

    private fun showPermissionDeniedDialog() {
        AlertDialog.Builder(this)
            .setTitle(getString(R.string.error_permission_denied))
            .setMessage("يحتاج التطبيق إلى الصلاحيات للعمل بشكل صحيح")
            .setPositiveButton("الإعدادات") { _, _ ->
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                    data = Uri.parse("package:$packageName")
                }
                startActivity(intent)
            }
            .setNegativeButton(getString(R.string.cancel)) { _, _ -> finish() }
            .show()
    }

    private fun enableCameraPreview() {
        if (cameraManager.isCameraAvailable()) {
            cameraManager.startCamera()
            binding.cameraContainer.visibility = View.VISIBLE
        } else {
            updateStatusText(getString(R.string.error_camera_not_available))
        }
    }

    private fun startTracking() {
        Log.d(TAG, "=== بدء التتبع - الحل الجذري ===")

        // فحص الصلاحيات الأساسية فقط
        if (!PermissionHelper.hasCameraPermission(this)) {
            updateStatusText("❌ صلاحية الكاميرا مطلوبة")
            requestCameraPermission()
            return
        }

        if (!PermissionHelper.hasOverlayPermission(this)) {
            updateStatusText("❌ صلاحية العرض مطلوبة")
            requestOverlayPermission()
            return
        }

        // بدء التتبع فوراً بدون انتظار خدمة إمكانية الوصول
        isTrackingActive = true
        binding.startTrackingButton.text = "إيقاف التتبع"
        updateStatusText("🚀 جاري بدء التتبع...")

        // Start analytics session
        analyticsManager.startSession()

        // بدء الخدمة مع إجبار إظهار المؤشر
        startTrackingServiceForced()

        Log.d(TAG, "=== تم بدء التتبع بنجاح ===")
    }

    private fun startTrackingServiceForced() {
        try {
            // إيقاف أي خدمة سابقة
            val stopIntent = Intent(this, EyeTrackingService::class.java)
            stopService(stopIntent)

            // انتظار قصير ثم بدء الخدمة
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                try {
                    val serviceIntent = Intent(this, EyeTrackingService::class.java)
                    serviceIntent.putExtra("FORCE_SHOW_CURSOR", true)
                    startForegroundService(serviceIntent)

                    updateStatusText("✅ تم بدء الخدمة - انتظار المؤشر...")

                    // فحص المؤشر كل ثانية لمدة 10 ثوان
                    startCursorCheckLoop()

                } catch (e: Exception) {
                    Log.e(TAG, "خطأ في بدء الخدمة", e)
                    updateStatusText("❌ خطأ في بدء الخدمة: ${e.message}")
                }
            }, 500)

        } catch (e: Exception) {
            Log.e(TAG, "خطأ في إيقاف الخدمة السابقة", e)
            updateStatusText("❌ خطأ: ${e.message}")
        }
    }

    private fun startCursorCheckLoop() {
        var checkCount = 0
        val maxChecks = 10

        val checkRunnable = object : Runnable {
            override fun run() {
                checkCount++
                Log.d(TAG, "فحص المؤشر - المحاولة $checkCount من $maxChecks")

                val service = EyeTrackingService.getInstance()
                if (service != null && service.isCursorVisible()) {
                    updateStatusText("🎉 المؤشر يعمل بنجاح!")
                    Log.d(TAG, "✅ المؤشر ظاهر ويعمل!")

                    // فحص خدمة إمكانية الوصول
                    if (!PermissionHelper.isAccessibilityServiceEnabled(this@MainActivity)) {
                        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                            showAccessibilityInstructionsNonBlocking()
                        }, 2000)
                    }
                    return
                }

                if (checkCount < maxChecks) {
                    updateStatusText("⏳ جاري إظهار المؤشر... ($checkCount/$maxChecks)")

                    // محاولة إجبار إظهار المؤشر
                    service?.forceShowCursor()

                    // فحص مرة أخرى بعد ثانية
                    android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(this, 1000)
                } else {
                    // فشل في إظهار المؤشر
                    updateStatusText("❌ فشل في إظهار المؤشر")
                    showCursorTroubleshootingDialog()
                }
            }
        }

        // بدء الفحص بعد ثانية واحدة
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(checkRunnable, 1000)
    }

    private fun stopTracking() {
        isTrackingActive = false
        binding.startTrackingButton.text = getString(R.string.start_tracking)

        // End analytics session
        analyticsManager.endSession()

        // Stop eye tracking service
        val serviceIntent = Intent(this, EyeTrackingService::class.java)
        stopService(serviceIntent)

        updateStatusText(getString(R.string.no_face_detected))
        Log.d(TAG, "Eye tracking stopped")
    }

    private fun onTrackingStatusChanged(status: TrackingStatus, face: Face?) {
        runOnUiThread {
            val statusText = when (status) {
                TrackingStatus.NO_FACE -> getString(R.string.no_face_detected)
                TrackingStatus.MULTIPLE_FACES -> getString(R.string.multiple_faces_detected)
                TrackingStatus.FACE_DETECTED -> getString(R.string.face_detected)
                TrackingStatus.LEFT_EYE_CLOSED -> getString(R.string.left_eye_closed)
                TrackingStatus.RIGHT_EYE_CLOSED -> getString(R.string.right_eye_closed)
                TrackingStatus.BOTH_EYES_CLOSED -> getString(R.string.both_eyes_closed)
                TrackingStatus.TRACKING_ACTIVE -> getString(R.string.tracking_active)
                TrackingStatus.ERROR -> getString(R.string.error_face_detection_failed)
                else -> "Unknown status"
            }
            updateStatusText(statusText)
        }
    }

    private fun updateStatusText(text: String) {
        binding.statusText.text = text
    }

    private fun showAccessibilityInstructionsNonBlocking() {
        val deviceBrand = android.os.Build.BRAND.lowercase()
        val instructions = when {
            deviceBrand.contains("realme") || deviceBrand.contains("oppo") -> {
                """
                المؤشر يعمل الآن!
                لتفعيل النقر بالغمزات:

                الإعدادات → إعدادات إضافية → إمكانية الوصول → الخدمات
                ابحث عن "المؤشر البصري" وفعّله
                """.trimIndent()
            }
            else -> {
                """
                المؤشر يعمل الآن!
                لتفعيل النقر بالغمزات:

                الإعدادات → إمكانية الوصول → الخدمات المثبتة
                ابحث عن "المؤشر البصري" وفعّله
                """.trimIndent()
            }
        }

        AlertDialog.Builder(this)
            .setTitle("تفعيل النقر بالغمزات")
            .setMessage(instructions)
            .setPositiveButton("فتح الإعدادات") { _, _ ->
                PermissionHelper.requestAccessibilityPermission(this)
            }
            .setNegativeButton("لاحقاً") { dialog, _ -> dialog.dismiss() }
            .show()
    }

    private fun showAccessibilityInstructions() {
        val deviceBrand = android.os.Build.BRAND.lowercase()
        val instructions = when {
            deviceBrand.contains("realme") || deviceBrand.contains("oppo") -> {
                """
                تعليمات خاصة لأجهزة Realme/Oppo:

                1. اذهب إلى: الإعدادات
                2. إعدادات إضافية
                3. إمكانية الوصول
                4. الخدمات أو الخدمات المثبتة
                5. ابحث عن "المؤشر البصري"
                6. فعّل المفتاح
                7. اضغط "موافق" على رسالة التحذير
                8. أعد تشغيل الجهاز
                9. ارجع للتطبيق واضغط "بدء التتبع"

                ملاحظة: قد تحتاج لتكرار هذه الخطوات
                """.trimIndent()
            }
            deviceBrand.contains("xiaomi") || deviceBrand.contains("redmi") -> {
                """
                تعليمات خاصة لأجهزة Xiaomi:

                1. الإعدادات → إعدادات إضافية → إمكانية الوصول
                2. ابحث عن "المؤشر البصري" وفعّله
                3. الإعدادات → التطبيقات → إدارة التطبيقات
                4. المؤشر البصري → صلاحيات أخرى → العرض فوق التطبيقات
                """.trimIndent()
            }
            else -> {
                """
                تعليمات عامة:

                1. الإعدادات → إمكانية الوصول
                2. الخدمات المثبتة أو الخدمات
                3. ابحث عن "المؤشر البصري"
                4. فعّل الخدمة
                5. اضغط "موافق" على رسالة التحذير
                """.trimIndent()
            }
        }

        AlertDialog.Builder(this)
            .setTitle("تفعيل خدمة إمكانية الوصول")
            .setMessage(instructions)
            .setPositiveButton("فتح الإعدادات") { _, _ ->
                PermissionHelper.requestAccessibilityPermission(this)
            }
            .setNegativeButton("إلغاء") { dialog, _ -> dialog.dismiss() }
            .setCancelable(false)
            .show()
    }

    private fun showHelpDialog() {
        val helpMessage = """
            كيفية استخدام المؤشر البصري:

            1. امنح التطبيق صلاحيات الكاميرا والعرض
            2. اضغط على "بدء التتبع"
            3. انظر إلى الشاشة لتحريك المؤشر
            4. اغمز بالعين اليمنى للنقر
            5. اغمز بالعين اليسرى للرجوع
            6. اغمز بكلتا العينين لفتح القائمة

            نصائح:
            - تأكد من وجود إضاءة جيدة
            - حافظ على مسافة مناسبة من الشاشة
            - استخدم المعايرة لتحسين الدقة
        """.trimIndent()

        AlertDialog.Builder(this)
            .setTitle(getString(R.string.help))
            .setMessage(helpMessage)
            .setPositiveButton(getString(R.string.ok)) { dialog, _ -> dialog.dismiss() }
            .show()
    }

    private fun checkCursorStatus() {
        if (!isTrackingActive) return

        val service = EyeTrackingService.getInstance()
        if (service != null) {
            if (service.isCursorVisible()) {
                updateStatusText("المؤشر يعمل بنجاح! ✅")
                Log.d(TAG, "Cursor is visible and working")
            } else {
                updateStatusText("المؤشر غير ظاهر - جاري المحاولة مرة أخرى...")
                Log.w(TAG, "Cursor not visible, trying to show again")
                service.forceShowCursor()

                // محاولة أخرى بعد ثانيتين
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    if (service.isCursorVisible()) {
                        updateStatusText("المؤشر يعمل الآن! ✅")
                    } else {
                        updateStatusText("مشكلة في إظهار المؤشر - تحقق من صلاحية العرض")
                        showCursorTroubleshootingDialog()
                    }
                }, 2000)
            }
        } else {
            updateStatusText("خطأ في بدء الخدمة")
            Log.e(TAG, "EyeTrackingService instance is null")
        }
    }

    private fun showCursorTroubleshootingDialog() {
        val deviceBrand = android.os.Build.BRAND.lowercase()
        val deviceModel = android.os.Build.MODEL

        val troubleshootingSteps = when {
            deviceBrand.contains("realme") -> """
                حل مشاكل Realme 7 Pro:

                🔧 الحل الأول:
                1. الإعدادات → التطبيقات → المؤشر البصري
                2. الصلاحيات → صلاحيات خاصة
                3. "العرض فوق التطبيقات الأخرى" → فعّل
                4. أعد تشغيل الجهاز

                🔧 الحل الثاني:
                1. الإعدادات → البطارية → تحسين البطارية
                2. ابحث عن "المؤشر البصري"
                3. اختر "عدم التحسين"

                🔧 الحل الثالث:
                1. الإعدادات → الخصوصية → مدير الصلاحيات
                2. صلاحيات خاصة → العرض فوق التطبيقات
                3. المؤشر البصري → السماح

                🔧 الحل الأخير:
                احذف التطبيق وأعد تثبيته
            """.trimIndent()

            else -> """
                خطوات حل المشكلة:

                1. تأكد من صلاحية "العرض فوق التطبيقات"
                2. أعد تشغيل الجهاز
                3. أعد تثبيت التطبيق
                4. تواصل مع المطور
            """.trimIndent()
        }

        AlertDialog.Builder(this)
            .setTitle("❌ المؤشر لا يظهر")
            .setMessage("الجهاز: $deviceBrand $deviceModel\n\n$troubleshootingSteps")
            .setPositiveButton("فتح إعدادات التطبيق") { _, _ ->
                PermissionHelper.openAppSettings(this)
            }
            .setNeutralButton("تواصل مع المطور") { _, _ ->
                // فتح واتساب أو نسخ الرقم
                try {
                    val intent = Intent(Intent.ACTION_VIEW)
                    intent.data = android.net.Uri.parse("https://wa.me/201062606098?text=مشكلة في المؤشر البصري - $deviceBrand $deviceModel")
                    startActivity(intent)
                } catch (e: Exception) {
                    // نسخ الرقم للحافظة
                    val clipboard = getSystemService(android.content.Context.CLIPBOARD_SERVICE) as android.content.ClipboardManager
                    val clip = android.content.ClipData.newPlainText("رقم المطور", "01062606098")
                    clipboard.setPrimaryClip(clip)
                    android.widget.Toast.makeText(this, "تم نسخ رقم المطور: 01062606098", android.widget.Toast.LENGTH_LONG).show()
                }
            }
            .setNegativeButton("إلغاء") { dialog, _ -> dialog.dismiss() }
            .setCancelable(false)
            .show()
    }

    private fun showSensitivityDialog() {
        val prefsManager = com.motasem.visualpointer.utils.PreferencesManager(this)
        val currentSensitivity = prefsManager.getEyeTrackingSensitivity()

        // إنشاء layout مخصص للإعدادات المتطورة
        val scrollView = android.widget.ScrollView(this)
        val mainLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(50, 30, 50, 30)
        }

        // عنوان التوضيح
        val titleText = android.widget.TextView(this).apply {
            text = "🎯 إعدادات تتبع الوجه المتطورة\n\nالآن يتم التتبع باستخدام الأنف والفم بدلاً من العين للراحة والدقة"
            textSize = 14f
            setPadding(0, 0, 0, 30)
            gravity = android.view.Gravity.CENTER
        }
        mainLayout.addView(titleText)

        // شريط الحساسية العامة
        val sensitivityLabel = android.widget.TextView(this).apply {
            text = "الحساسية العامة: $currentSensitivity%"
            textSize = 16f
            setPadding(0, 20, 0, 10)
        }
        mainLayout.addView(sensitivityLabel)

        val sensitivitySeekBar = android.widget.SeekBar(this).apply {
            max = 100
            progress = currentSensitivity
            setPadding(0, 0, 0, 20)
        }
        mainLayout.addView(sensitivitySeekBar)

        // إعدادات سريعة
        val quickSettingsLabel = android.widget.TextView(this).apply {
            text = "⚡ إعدادات سريعة:"
            textSize = 16f
            setPadding(0, 20, 0, 10)
            setTypeface(null, android.graphics.Typeface.BOLD)
        }
        mainLayout.addView(quickSettingsLabel)

        // أزرار الإعدادات السريعة
        val buttonLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            setPadding(0, 10, 0, 20)
        }

        val slowButton = android.widget.Button(this).apply {
            text = "🐌 بطيء\n(40%)"
            setOnClickListener {
                sensitivitySeekBar.progress = 40
                sensitivityLabel.text = "الحساسية العامة: 40%"
            }
        }

        val normalButton = android.widget.Button(this).apply {
            text = "⚖️ عادي\n(70%)"
            setOnClickListener {
                sensitivitySeekBar.progress = 70
                sensitivityLabel.text = "الحساسية العامة: 70%"
            }
        }

        val fastButton = android.widget.Button(this).apply {
            text = "🚀 سريع\n(90%)"
            setOnClickListener {
                sensitivitySeekBar.progress = 90
                sensitivityLabel.text = "الحساسية العامة: 90%"
            }
        }

        buttonLayout.addView(slowButton)
        buttonLayout.addView(normalButton)
        buttonLayout.addView(fastButton)
        mainLayout.addView(buttonLayout)

        // نصائح الاستخدام
        val tipsText = android.widget.TextView(this).apply {
            text = """
                💡 نصائح للحصول على أفضل أداء:

                🎯 للدقة العالية: 40-60%
                ⚖️ للاستخدام العادي: 70-80%
                🚀 للاستجابة السريعة: 85-95%

                📱 تأكد من:
                • الجلوس على مسافة 30-50 سم
                • وجود إضاءة جيدة على الوجه
                • ثبات الرأس نسبياً أثناء الاستخدام
            """.trimIndent()
            textSize = 12f
            setPadding(0, 20, 0, 0)
            setBackgroundColor(android.graphics.Color.parseColor("#F0F0F0"))
            setPadding(20, 20, 20, 20)
        }
        mainLayout.addView(tipsText)

        // تحديث النص عند تغيير الشريط
        sensitivitySeekBar.setOnSeekBarChangeListener(object : android.widget.SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: android.widget.SeekBar?, progress: Int, fromUser: Boolean) {
                sensitivityLabel.text = "الحساسية العامة: $progress%"
            }
            override fun onStartTrackingTouch(seekBar: android.widget.SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: android.widget.SeekBar?) {}
        })

        scrollView.addView(mainLayout)

        AlertDialog.Builder(this)
            .setTitle("⚙️ إعدادات التتبع المتطورة")
            .setView(scrollView)
            .setPositiveButton("💾 حفظ") { _, _ ->
                val newSensitivity = sensitivitySeekBar.progress
                prefsManager.setEyeTrackingSensitivity(newSensitivity)
                updateStatusText("✅ تم حفظ الحساسية: $newSensitivity%")

                // إعادة تشغيل التتبع إذا كان نشط
                if (isTrackingActive) {
                    val service = EyeTrackingService.getInstance()
                    service?.getEyeTracker()?.reset()
                }
            }
            .setNeutralButton("🔄 افتراضي") { _, _ ->
                prefsManager.setEyeTrackingSensitivity(70) // القيمة الافتراضية المحسنة
                updateStatusText("تم إعادة تعيين الحساسية إلى 70%")
            }
            .setNegativeButton("❌ إلغاء") { dialog, _ -> dialog.dismiss() }
            .show()
    }

    private fun showDiagnosticDialog() {
        val service = EyeTrackingService.getInstance()
        val diagnosticInfo = StringBuilder()

        diagnosticInfo.append("=== تشخيص شامل للمؤشر البصري ===\n\n")

        // معلومات الجهاز
        diagnosticInfo.append("الجهاز: ${android.os.Build.BRAND} ${android.os.Build.MODEL}\n")
        diagnosticInfo.append("أندرويد: ${android.os.Build.VERSION.RELEASE} (API ${android.os.Build.VERSION.SDK_INT})\n\n")

        // حالة الصلاحيات
        diagnosticInfo.append("=== الصلاحيات ===\n")
        diagnosticInfo.append("الكاميرا: ${PermissionHelper.hasCameraPermission(this)}\n")
        diagnosticInfo.append("العرض: ${PermissionHelper.hasOverlayPermission(this)}\n")
        diagnosticInfo.append("إمكانية الوصول: ${PermissionHelper.isAccessibilityServiceEnabled(this)}\n\n")

        // حالة الخدمة
        diagnosticInfo.append("=== الخدمة ===\n")
        diagnosticInfo.append("خدمة التتبع: ${service != null}\n")
        diagnosticInfo.append("حالة التتبع: $isTrackingActive\n")

        if (service != null) {
            diagnosticInfo.append("المؤشر ظاهر: ${service.isCursorVisible()}\n")
            // إضافة معلومات تشخيص المؤشر إذا كانت متوفرة
        }

        diagnosticInfo.append("\n=== إرشادات ===\n")
        diagnosticInfo.append("• اضغط مطولاً على العنوان لإظهار هذا التشخيص\n")
        diagnosticInfo.append("• أرسل هذه المعلومات للمطور عند طلب المساعدة\n")

        AlertDialog.Builder(this)
            .setTitle("🔍 تشخيص النظام")
            .setMessage(diagnosticInfo.toString())
            .setPositiveButton("نسخ للحافظة") { _, _ ->
                val clipboard = getSystemService(android.content.Context.CLIPBOARD_SERVICE) as android.content.ClipboardManager
                val clip = android.content.ClipData.newPlainText("تشخيص المؤشر البصري", diagnosticInfo.toString())
                clipboard.setPrimaryClip(clip)
                android.widget.Toast.makeText(this, "تم نسخ التشخيص للحافظة", android.widget.Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("إغلاق") { dialog, _ -> dialog.dismiss() }
            .show()
    }

    private fun showBackgroundRunningTips() {
        val deviceBrand = android.os.Build.BRAND.lowercase()

        val tips = when {
            deviceBrand.contains("realme") || deviceBrand.contains("oppo") -> """
                💡 نصائح للحفاظ على المؤشر نشط:

                🔋 إعدادات البطارية:
                • الإعدادات → البطارية → تحسين البطارية
                • ابحث عن "المؤشر البصري" → عدم التحسين

                🚀 التشغيل التلقائي:
                • الإعدادات → البطارية → التشغيل التلقائي
                • فعّل "المؤشر البصري"

                🔒 قفل التطبيق:
                • من قائمة التطبيقات الحديثة
                • اسحب لأسفل على "المؤشر البصري"
                • اضغط على أيقونة القفل 🔒

                ⚠️ مهم: لا تمسح التطبيق من الذاكرة!
            """.trimIndent()

            else -> """
                💡 للحفاظ على المؤشر نشط:

                • لا تمسح التطبيق من الذاكرة
                • فعّل "عدم تحسين البطارية" للتطبيق
                • اقفل التطبيق في قائمة التطبيقات الحديثة
            """.trimIndent()
        }

        // إظهار النصائح مرة واحدة فقط
        val prefs = getSharedPreferences("app_prefs", MODE_PRIVATE)
        val tipsShown = prefs.getBoolean("background_tips_shown", false)

        if (!tipsShown) {
            AlertDialog.Builder(this)
                .setTitle("🔄 التطبيق يعمل في الخلفية")
                .setMessage(tips)
                .setPositiveButton("فهمت") { _, _ ->
                    prefs.edit().putBoolean("background_tips_shown", true).apply()
                }
                .setNeutralButton("إعدادات البطارية") { _, _ ->
                    try {
                        val intent = Intent(android.provider.Settings.ACTION_IGNORE_BATTERY_OPTIMIZATION_SETTINGS)
                        startActivity(intent)
                    } catch (e: Exception) {
                        // فتح إعدادات التطبيق كبديل
                        PermissionHelper.openAppSettings(this)
                    }
                    prefs.edit().putBoolean("background_tips_shown", true).apply()
                }
                .setCancelable(false)
                .show()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (::cameraManager.isInitialized) {
            cameraManager.stopCamera()
        }
        if (isTrackingActive) {
            stopTracking()
        }
    }
}
