package com.motasem.visualpointer.utils

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.provider.Settings
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

/**
 * Helper class for managing app permissions
 */
object PermissionHelper {

    const val CAMERA_PERMISSION_REQUEST_CODE = 1001
    const val OVERLAY_PERMISSION_REQUEST_CODE = 1002
    const val ACCESSIBILITY_PERMISSION_REQUEST_CODE = 1003

    /**
     * Check if camera permission is granted
     */
    fun hasCameraPermission(context: Context): Bo<PERSON>an {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.CAMERA
        ) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * Check if overlay permission is granted
     */
    fun hasOverlayPermission(context: Context): Boolean {
        return Settings.canDrawOverlays(context)
    }

    /**
     * Check if accessibility service is enabled
     */
    fun isAccessibilityServiceEnabled(context: Context): Boolean {
        val accessibilityEnabled = Settings.Secure.getInt(
            context.contentResolver,
            Settings.Secure.ACCESSIBILITY_ENABLED,
            0
        )

        android.util.Log.d("PermissionHelper", "Accessibility enabled: $accessibilityEnabled")

        if (accessibilityEnabled == 1) {
            val services = Settings.Secure.getString(
                context.contentResolver,
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
            )

            android.util.Log.d("PermissionHelper", "Enabled services: $services")

            // جرب أسماء مختلفة للخدمة
            val possibleServiceNames = listOf(
                "${context.packageName}/.service.VisualPointerAccessibilityService",
                "${context.packageName}/com.motasem.visualpointer.service.VisualPointerAccessibilityService",
                "com.motasem.visualpointer/.service.VisualPointerAccessibilityService",
                "com.motasem.visualpointer/com.motasem.visualpointer.service.VisualPointerAccessibilityService"
            )

            for (serviceName in possibleServiceNames) {
                if (services?.contains(serviceName) == true) {
                    android.util.Log.d("PermissionHelper", "Service found: $serviceName")
                    return true
                }
            }

            android.util.Log.d("PermissionHelper", "Service not found in enabled services")
        }

        return false
    }

    /**
     * Request camera permission
     */
    fun requestCameraPermission(activity: Activity) {
        ActivityCompat.requestPermissions(
            activity,
            arrayOf(Manifest.permission.CAMERA),
            CAMERA_PERMISSION_REQUEST_CODE
        )
    }

    /**
     * Request overlay permission
     */
    fun requestOverlayPermission(activity: Activity) {
        val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION).apply {
            data = Uri.parse("package:${activity.packageName}")
        }
        activity.startActivityForResult(intent, OVERLAY_PERMISSION_REQUEST_CODE)
    }

    /**
     * Request accessibility service permission
     */
    fun requestAccessibilityPermission(activity: Activity) {
        val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
        activity.startActivityForResult(intent, ACCESSIBILITY_PERMISSION_REQUEST_CODE)
    }

    /**
     * Check if accessibility service is running (alternative method)
     */
    fun isAccessibilityServiceRunning(context: Context): Boolean {
        try {
            // طريقة بديلة للتحقق من الخدمة
            val serviceClass = "com.motasem.visualpointer.service.VisualPointerAccessibilityService"
            val manager = context.getSystemService(Context.ACCESSIBILITY_SERVICE) as android.view.accessibility.AccessibilityManager

            val enabledServices = Settings.Secure.getString(
                context.contentResolver,
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
            )

            android.util.Log.d("PermissionHelper", "Alternative check - Enabled services: $enabledServices")

            return enabledServices?.contains(context.packageName) == true
        } catch (e: Exception) {
            android.util.Log.e("PermissionHelper", "Error checking accessibility service", e)
            return false
        }
    }

    /**
     * Check if all required permissions are granted
     */
    fun hasAllPermissions(context: Context): Boolean {
        val camera = hasCameraPermission(context)
        val overlay = hasOverlayPermission(context)
        val accessibility = isAccessibilityServiceEnabled(context) || isAccessibilityServiceRunning(context)

        android.util.Log.d("PermissionHelper", "All permissions check - Camera: $camera, Overlay: $overlay, Accessibility: $accessibility")

        return camera && overlay && accessibility
    }

    /**
     * Get list of missing permissions
     */
    fun getMissingPermissions(context: Context): List<String> {
        val missingPermissions = mutableListOf<String>()
        
        if (!hasCameraPermission(context)) {
            missingPermissions.add("Camera")
        }
        
        if (!hasOverlayPermission(context)) {
            missingPermissions.add("Overlay")
        }
        
        if (!isAccessibilityServiceEnabled(context)) {
            missingPermissions.add("Accessibility Service")
        }
        
        return missingPermissions
    }

    /**
     * Open app settings page
     */
    fun openAppSettings(context: Context) {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = Uri.parse("package:${context.packageName}")
        }
        context.startActivity(intent)
    }

    /**
     * Check if permission should show rationale
     */
    fun shouldShowCameraPermissionRationale(activity: Activity): Boolean {
        return ActivityCompat.shouldShowRequestPermissionRationale(
            activity,
            Manifest.permission.CAMERA
        )
    }
}
