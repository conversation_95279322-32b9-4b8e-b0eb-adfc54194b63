package com.motasem.visualpointer.camera

import android.graphics.Rect
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import com.google.android.gms.tasks.Task
import com.google.mlkit.vision.common.InputImage
import com.motasem.visualpointer.ui.GraphicOverlay

/**
 * Abstract base class for image analysis
 */
abstract class Analyzer<T> : ImageAnalysis.Analyzer {

    abstract val graphicOverlay: GraphicOverlay

    override fun analyze(imageProxy: ImageProxy) {
        val mediaImage = imageProxy.image
        if (mediaImage != null) {
            val image = InputImage.fromMediaImage(mediaImage, imageProxy.imageInfo.rotationDegrees)
            val rect = Rect(0, 0, mediaImage.width, mediaImage.height)
            
            detectInImage(image)
                .addOnSuccessListener { results ->
                    onSuccess(results, graphicOverlay, rect, imageProxy)
                }
                .addOnFailureListener { exception ->
                    onFailure(exception)
                }
                .addOnCompleteListener {
                    imageProxy.close()
                }
        } else {
            imageProxy.close()
        }
    }

    protected abstract fun detectInImage(image: InputImage): Task<T>

    abstract fun stop()

    protected abstract fun onSuccess(
        results: T,
        graphicOverlay: GraphicOverlay,
        rect: Rect,
        imageProxy: ImageProxy
    )

    protected abstract fun onFailure(exception: Exception)
}
