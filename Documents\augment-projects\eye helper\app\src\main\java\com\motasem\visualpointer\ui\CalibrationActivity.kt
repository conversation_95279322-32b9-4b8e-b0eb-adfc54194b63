package com.motasem.visualpointer.ui

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.DisplayMetrics
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.google.mlkit.vision.face.Face
import com.motasem.visualpointer.R
import com.motasem.visualpointer.camera.CameraManager
import com.motasem.visualpointer.databinding.ActivityCalibrationBinding
import com.motasem.visualpointer.model.TrackingStatus
import com.motasem.visualpointer.utils.PreferencesManager

class CalibrationActivity : AppCompatActivity() {

    private lateinit var binding: ActivityCalibrationBinding
    private lateinit var cameraManager: CameraManager
    private lateinit var preferencesManager: PreferencesManager
    
    private val calibrationPoints = mutableListOf<Pair<Float, Float>>()
    private val gazePoints = mutableListOf<Pair<Float, Float>>()
    private var currentPointIndex = 0
    private var isCalibrating = false
    private var screenWidth = 0
    private var screenHeight = 0
    
    private val handler = Handler(Looper.getMainLooper())

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCalibrationBinding.inflate(layoutInflater)
        setContentView(binding.root)

        preferencesManager = PreferencesManager(this)
        getScreenDimensions()
        setupCalibrationPoints()
        setupUI()
        setupCamera()
    }

    private fun getScreenDimensions() {
        val displayMetrics = DisplayMetrics()
        windowManager.defaultDisplay.getMetrics(displayMetrics)
        screenWidth = displayMetrics.widthPixels
        screenHeight = displayMetrics.heightPixels
    }

    private fun setupCalibrationPoints() {
        // Create a 3x3 grid of calibration points
        val marginX = screenWidth * 0.1f
        val marginY = screenHeight * 0.1f
        
        for (row in 0..2) {
            for (col in 0..2) {
                val x = marginX + (screenWidth - 2 * marginX) * col / 2
                val y = marginY + (screenHeight - 2 * marginY) * row / 2
                calibrationPoints.add(Pair(x, y))
            }
        }
    }

    private fun setupUI() {
        binding.startCalibrationButton.setOnClickListener {
            startCalibration()
        }

        binding.skipCalibrationButton.setOnClickListener {
            skipCalibration()
        }

        binding.backFromCalibrationButton.setOnClickListener {
            finish()
        }
    }

    private fun setupCamera() {
        cameraManager = CameraManager(
            this,
            binding.calibrationPreviewView,
            this,
            binding.calibrationGraphicOverlay,
            ::onTrackingStatusChanged
        )
        cameraManager.startCamera()
    }

    private fun startCalibration() {
        if (isCalibrating) return

        isCalibrating = true
        currentPointIndex = 0
        gazePoints.clear()
        
        binding.startCalibrationButton.visibility = View.GONE
        binding.skipCalibrationButton.visibility = View.GONE
        binding.calibrationProgressBar.visibility = View.VISIBLE
        binding.calibrationProgressBar.max = calibrationPoints.size
        
        showNextCalibrationPoint()
    }

    private fun showNextCalibrationPoint() {
        if (currentPointIndex >= calibrationPoints.size) {
            completeCalibration()
            return
        }

        val point = calibrationPoints[currentPointIndex]
        
        // Update progress
        binding.calibrationProgress.text = getString(
            R.string.calibration_point,
            currentPointIndex + 1,
            calibrationPoints.size
        )
        binding.calibrationProgressBar.progress = currentPointIndex

        // Position the calibration point
        binding.calibrationPoint.x = point.first - binding.calibrationPoint.width / 2
        binding.calibrationPoint.y = point.second - binding.calibrationPoint.height / 2
        binding.calibrationPoint.visibility = View.VISIBLE

        // Animate the point
        animateCalibrationPoint()

        // Wait for user to look at the point
        handler.postDelayed({
            collectGazeData()
        }, 2000) // 2 seconds to look at the point
    }

    private fun animateCalibrationPoint() {
        val scaleX = ObjectAnimator.ofFloat(binding.calibrationPoint, "scaleX", 0.5f, 1.2f, 1.0f)
        val scaleY = ObjectAnimator.ofFloat(binding.calibrationPoint, "scaleY", 0.5f, 1.2f, 1.0f)
        val alpha = ObjectAnimator.ofFloat(binding.calibrationPoint, "alpha", 0.3f, 1.0f)

        val animatorSet = AnimatorSet()
        animatorSet.playTogether(scaleX, scaleY, alpha)
        animatorSet.duration = 1000
        animatorSet.interpolator = AccelerateDecelerateInterpolator()
        animatorSet.start()
    }

    private fun collectGazeData() {
        // In a real implementation, this would collect the actual gaze data
        // For now, we'll simulate it with the calibration point position
        val point = calibrationPoints[currentPointIndex]
        gazePoints.add(point)
        
        currentPointIndex++
        
        handler.postDelayed({
            showNextCalibrationPoint()
        }, 500)
    }

    private fun completeCalibration() {
        isCalibrating = false
        binding.calibrationPoint.visibility = View.GONE
        binding.calibrationProgressBar.visibility = View.GONE
        
        // Save calibration data
        val calibrationData = createCalibrationData()
        preferencesManager.setCalibrationData(calibrationData)
        
        binding.calibrationInstruction.text = getString(R.string.calibration_complete)
        
        Toast.makeText(this, getString(R.string.calibration_complete), Toast.LENGTH_LONG).show()
        
        handler.postDelayed({
            finish()
        }, 2000)
    }

    private fun skipCalibration() {
        Toast.makeText(this, "تم تخطي المعايرة", Toast.LENGTH_SHORT).show()
        finish()
    }

    private fun createCalibrationData(): String {
        // Create a simple calibration data string
        // In a real implementation, this would be more sophisticated
        val data = StringBuilder()
        for (i in calibrationPoints.indices) {
            val calibPoint = calibrationPoints[i]
            val gazePoint = gazePoints.getOrNull(i) ?: calibPoint
            data.append("${calibPoint.first},${calibPoint.second}:${gazePoint.first},${gazePoint.second};")
        }
        return data.toString()
    }

    private fun onTrackingStatusChanged(status: TrackingStatus, face: Face?) {
        // Handle face tracking status during calibration
        runOnUiThread {
            when (status) {
                TrackingStatus.NO_FACE -> {
                    binding.calibrationInstruction.text = "يرجى وضع وجهك أمام الكاميرا"
                }
                TrackingStatus.MULTIPLE_FACES -> {
                    binding.calibrationInstruction.text = "يرجى التأكد من وجود شخص واحد فقط"
                }
                TrackingStatus.FACE_DETECTED -> {
                    if (!isCalibrating) {
                        binding.calibrationInstruction.text = getString(R.string.calibration_instruction)
                    }
                }
                else -> {
                    // Handle other statuses if needed
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (::cameraManager.isInitialized) {
            cameraManager.stopCamera()
        }
        handler.removeCallbacksAndMessages(null)
    }
}
