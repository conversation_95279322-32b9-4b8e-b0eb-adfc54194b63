package com.motasem.visualpointer.ui

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.DisplayMetrics
import android.util.Log
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.ImageView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.google.mlkit.vision.face.Face
import com.motasem.visualpointer.R
import com.motasem.visualpointer.camera.CameraManager
import com.motasem.visualpointer.databinding.ActivityCalibrationBinding
import com.motasem.visualpointer.model.TrackingStatus
import com.motasem.visualpointer.service.EyeTrackingService
import com.motasem.visualpointer.tracking.EyeTracker
import com.motasem.visualpointer.utils.PreferencesManager
import kotlin.math.abs
import kotlin.math.sqrt

class CalibrationActivity : AppCompatActivity() {

    private lateinit var binding: ActivityCalibrationBinding
    private lateinit var cameraManager: CameraManager
    private lateinit var preferencesManager: PreferencesManager

    private val calibrationPoints = mutableListOf<Pair<Float, Float>>()
    private val gazePoints = mutableListOf<Pair<Float, Float>>()
    private var currentPointIndex = 0
    private var isCalibrating = false
    private var screenWidth = 0
    private var screenHeight = 0

    private val handler = Handler(Looper.getMainLooper())

    // Real-time cursor tracking
    private var realTimeCursor: ImageView? = null
    private var eyeTracker: EyeTracker? = null
    private var isShowingRealTimeCursor = false
    private var currentTargetX = 0f
    private var currentTargetY = 0f
    private var calibrationAccuracy = 0f

    companion object {
        private const val TAG = "CalibrationActivity"
        private const val CALIBRATION_THRESHOLD = 100f // pixels
        private const val COLLECTION_TIME = 3000L // 3 seconds
        private const val CURSOR_UPDATE_INTERVAL = 50L // 20 FPS
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCalibrationBinding.inflate(layoutInflater)
        setContentView(binding.root)

        preferencesManager = PreferencesManager(this)
        getScreenDimensions()
        setupCalibrationPoints()
        setupUI()
        setupCamera()
    }

    private fun getScreenDimensions() {
        val displayMetrics = DisplayMetrics()
        windowManager.defaultDisplay.getMetrics(displayMetrics)
        screenWidth = displayMetrics.widthPixels
        screenHeight = displayMetrics.heightPixels
    }

    private fun setupCalibrationPoints() {
        // Create a 3x3 grid of calibration points
        val marginX = screenWidth * 0.1f
        val marginY = screenHeight * 0.1f
        
        for (row in 0..2) {
            for (col in 0..2) {
                val x = marginX + (screenWidth - 2 * marginX) * col / 2
                val y = marginY + (screenHeight - 2 * marginY) * row / 2
                calibrationPoints.add(Pair(x, y))
            }
        }
    }

    private fun setupUI() {
        binding.startCalibrationButton.setOnClickListener {
            startCalibration()
        }

        binding.skipCalibrationButton.setOnClickListener {
            skipCalibration()
        }

        binding.backFromCalibrationButton.setOnClickListener {
            finish()
        }

        // Create real-time cursor
        createRealTimeCursor()

        // Get eye tracker from service
        val service = EyeTrackingService.getInstance()
        eyeTracker = service?.getEyeTracker()

        // Start real-time cursor updates
        startRealTimeCursorUpdates()
    }

    private fun createRealTimeCursor() {
        realTimeCursor = ImageView(this).apply {
            setImageResource(android.R.drawable.ic_menu_mylocation)
            layoutParams = android.widget.FrameLayout.LayoutParams(40, 40)
            setColorFilter(Color.RED)
            alpha = 0.8f
            visibility = View.GONE
        }

        // Add cursor to the root layout
        val rootLayout = findViewById<android.widget.FrameLayout>(android.R.id.content)
        rootLayout.addView(realTimeCursor)
    }

    private fun startRealTimeCursorUpdates() {
        isShowingRealTimeCursor = true
        updateRealTimeCursor()
    }

    private fun updateRealTimeCursor() {
        if (!isShowingRealTimeCursor) return

        // Get current face tracking data
        // This would be connected to the actual face tracking in a real implementation
        // For now, we'll simulate cursor movement

        handler.postDelayed({
            updateRealTimeCursor()
        }, CURSOR_UPDATE_INTERVAL)
    }

    private fun setupCamera() {
        cameraManager = CameraManager(
            this,
            binding.calibrationPreviewView,
            this,
            binding.calibrationGraphicOverlay,
            ::onTrackingStatusChanged
        )
        cameraManager.startCamera()
    }

    private fun startCalibration() {
        if (isCalibrating) return

        isCalibrating = true
        currentPointIndex = 0
        gazePoints.clear()

        binding.startCalibrationButton.visibility = View.GONE
        binding.skipCalibrationButton.visibility = View.GONE
        binding.calibrationProgressBar.visibility = View.VISIBLE
        binding.calibrationProgressBar.max = calibrationPoints.size

        // Show real-time cursor during calibration
        realTimeCursor?.visibility = View.VISIBLE

        // Update instruction
        binding.calibrationInstruction.text = "🎯 انظر للنقطة الحمراء وحرك رأسك لتوجيه المؤشر الأحمر إليها"

        showNextCalibrationPoint()
    }

    private fun showNextCalibrationPoint() {
        if (currentPointIndex >= calibrationPoints.size) {
            completeCalibration()
            return
        }

        val point = calibrationPoints[currentPointIndex]
        currentTargetX = point.first
        currentTargetY = point.second

        // Update progress
        binding.calibrationProgress.text = "النقطة ${currentPointIndex + 1} من ${calibrationPoints.size}"
        binding.calibrationProgressBar.progress = currentPointIndex

        // Position the calibration point
        binding.calibrationPoint.x = point.first - binding.calibrationPoint.width / 2
        binding.calibrationPoint.y = point.second - binding.calibrationPoint.height / 2
        binding.calibrationPoint.visibility = View.VISIBLE

        // Animate the point
        animateCalibrationPoint()

        // Update instruction with real-time feedback
        binding.calibrationInstruction.text = "🎯 حرك رأسك لتوجيه المؤشر الأحمر للنقطة الزرقاء"

        // Start collecting gaze data with real-time feedback
        startRealTimeCalibrationCollection()
    }

    private fun startRealTimeCalibrationCollection() {
        val startTime = System.currentTimeMillis()
        val collectData = mutableListOf<Pair<Float, Float>>()

        val collectionRunnable = object : Runnable {
            override fun run() {
                val elapsed = System.currentTimeMillis() - startTime

                if (elapsed >= COLLECTION_TIME) {
                    // Collection complete for this point
                    finishPointCollection(collectData)
                    return
                }

                // Simulate getting current cursor position (in real implementation, get from eye tracker)
                val currentCursorX = realTimeCursor?.x ?: 0f
                val currentCursorY = realTimeCursor?.y ?: 0f

                // Calculate distance to target
                val distance = sqrt(
                    (currentCursorX - currentTargetX) * (currentCursorX - currentTargetX) +
                    (currentCursorY - currentTargetY) * (currentCursorY - currentTargetY)
                )

                // Provide visual feedback
                updateCalibrationFeedback(distance)

                // Collect data if cursor is close enough
                if (distance < CALIBRATION_THRESHOLD) {
                    collectData.add(Pair(currentCursorX, currentCursorY))
                }

                // Update progress
                val progress = (elapsed.toFloat() / COLLECTION_TIME * 100).toInt()
                binding.calibrationProgress.text = "النقطة ${currentPointIndex + 1} من ${calibrationPoints.size} - $progress%"

                handler.postDelayed(this, 100) // Update every 100ms
            }
        }

        handler.post(collectionRunnable)
    }

    private fun updateCalibrationFeedback(distance: Float) {
        val calibrationPoint = binding.calibrationPoint

        when {
            distance < CALIBRATION_THRESHOLD / 3 -> {
                // Very close - green
                calibrationPoint.setBackgroundColor(Color.GREEN)
                binding.calibrationInstruction.text = "✅ ممتاز! استمر في النظر هنا"
            }
            distance < CALIBRATION_THRESHOLD -> {
                // Close - yellow
                calibrationPoint.setBackgroundColor(Color.YELLOW)
                binding.calibrationInstruction.text = "🎯 قريب جداً! حافظ على هذا الموضع"
            }
            else -> {
                // Far - blue (default)
                calibrationPoint.setBackgroundColor(Color.BLUE)
                binding.calibrationInstruction.text = "🔄 حرك رأسك لتوجيه المؤشر للنقطة"
            }
        }
    }

    private fun finishPointCollection(collectData: List<Pair<Float, Float>>) {
        if (collectData.isNotEmpty()) {
            // Calculate average position
            val avgX = collectData.map { it.first }.average().toFloat()
            val avgY = collectData.map { it.second }.average().toFloat()
            gazePoints.add(Pair(avgX, avgY))

            // Calculate accuracy for this point
            val targetPoint = calibrationPoints[currentPointIndex]
            val accuracy = sqrt(
                (avgX - targetPoint.first) * (avgX - targetPoint.first) +
                (avgY - targetPoint.second) * (avgY - targetPoint.second)
            )

            Log.d(TAG, "Point $currentPointIndex accuracy: $accuracy pixels")
        } else {
            // No data collected, use target point
            gazePoints.add(calibrationPoints[currentPointIndex])
        }

        currentPointIndex++

        // Brief pause before next point
        handler.postDelayed({
            showNextCalibrationPoint()
        }, 1000)
    }

    private fun animateCalibrationPoint() {
        val scaleX = ObjectAnimator.ofFloat(binding.calibrationPoint, "scaleX", 0.5f, 1.2f, 1.0f)
        val scaleY = ObjectAnimator.ofFloat(binding.calibrationPoint, "scaleY", 0.5f, 1.2f, 1.0f)
        val alpha = ObjectAnimator.ofFloat(binding.calibrationPoint, "alpha", 0.3f, 1.0f)

        val animatorSet = AnimatorSet()
        animatorSet.playTogether(scaleX, scaleY, alpha)
        animatorSet.duration = 1000
        animatorSet.interpolator = AccelerateDecelerateInterpolator()
        animatorSet.start()
    }

    private fun collectGazeData() {
        // In a real implementation, this would collect the actual gaze data
        // For now, we'll simulate it with the calibration point position
        val point = calibrationPoints[currentPointIndex]
        gazePoints.add(point)
        
        currentPointIndex++
        
        handler.postDelayed({
            showNextCalibrationPoint()
        }, 500)
    }

    private fun completeCalibration() {
        isCalibrating = false
        isShowingRealTimeCursor = false

        binding.calibrationPoint.visibility = View.GONE
        binding.calibrationProgressBar.visibility = View.GONE
        realTimeCursor?.visibility = View.GONE

        // Calculate overall calibration accuracy
        calculateCalibrationAccuracy()

        // Save calibration data
        val calibrationData = createCalibrationData()
        preferencesManager.setCalibrationData(calibrationData)

        // Apply calibration to eye tracker
        eyeTracker?.let { tracker ->
            val centerX = screenWidth / 2f
            val centerY = screenHeight / 2f
            tracker.calibrate(centerX, centerY)
        }

        // Show completion message with accuracy
        val accuracyText = if (calibrationAccuracy < 50) "ممتازة"
                          else if (calibrationAccuracy < 100) "جيدة"
                          else "مقبولة"

        binding.calibrationInstruction.text = "✅ تمت المعايرة بنجاح!\nدقة المعايرة: $accuracyText"
        binding.calibrationProgress.text = "دقة المعايرة: ${calibrationAccuracy.toInt()} بكسل"

        Toast.makeText(this, "تمت المعايرة بنجاح! دقة: $accuracyText", Toast.LENGTH_LONG).show()

        handler.postDelayed({
            finish()
        }, 3000)
    }

    private fun calculateCalibrationAccuracy() {
        var totalError = 0f
        var validPoints = 0

        for (i in calibrationPoints.indices) {
            val targetPoint = calibrationPoints[i]
            val gazePoint = gazePoints.getOrNull(i)

            if (gazePoint != null) {
                val error = sqrt(
                    (gazePoint.first - targetPoint.first) * (gazePoint.first - targetPoint.first) +
                    (gazePoint.second - targetPoint.second) * (gazePoint.second - targetPoint.second)
                )
                totalError += error
                validPoints++
            }
        }

        calibrationAccuracy = if (validPoints > 0) totalError / validPoints else 0f
        Log.d(TAG, "Overall calibration accuracy: $calibrationAccuracy pixels")
    }

    private fun skipCalibration() {
        Toast.makeText(this, "تم تخطي المعايرة", Toast.LENGTH_SHORT).show()
        finish()
    }

    private fun createCalibrationData(): String {
        // Create a simple calibration data string
        // In a real implementation, this would be more sophisticated
        val data = StringBuilder()
        for (i in calibrationPoints.indices) {
            val calibPoint = calibrationPoints[i]
            val gazePoint = gazePoints.getOrNull(i) ?: calibPoint
            data.append("${calibPoint.first},${calibPoint.second}:${gazePoint.first},${gazePoint.second};")
        }
        return data.toString()
    }

    private fun onTrackingStatusChanged(status: TrackingStatus, face: Face?) {
        // Handle face tracking status during calibration
        runOnUiThread {
            when (status) {
                TrackingStatus.NO_FACE -> {
                    binding.calibrationInstruction.text = "يرجى وضع وجهك أمام الكاميرا"
                }
                TrackingStatus.MULTIPLE_FACES -> {
                    binding.calibrationInstruction.text = "يرجى التأكد من وجود شخص واحد فقط"
                }
                TrackingStatus.FACE_DETECTED -> {
                    if (!isCalibrating) {
                        binding.calibrationInstruction.text = getString(R.string.calibration_instruction)
                    }
                }
                else -> {
                    // Handle other statuses if needed
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()

        // Stop real-time cursor updates
        isShowingRealTimeCursor = false

        // Remove real-time cursor
        realTimeCursor?.let { cursor ->
            val rootLayout = findViewById<android.widget.FrameLayout>(android.R.id.content)
            rootLayout.removeView(cursor)
        }

        if (::cameraManager.isInitialized) {
            cameraManager.stopCamera()
        }
        handler.removeCallbacksAndMessages(null)
    }
}
