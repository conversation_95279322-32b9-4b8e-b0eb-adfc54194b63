-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:2:1-117:12
INJECTED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:2:1-117:12
INJECTED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:2:1-117:12
INJECTED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:2:1-117:12
MERGED from [androidx.databinding:viewbinding:8.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3c4d7dce088827d4a62951b6df06cd6\transformed\viewbinding-8.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06fd2fa39dd57ce426fb9e8f08397def\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4fa19f2f35e2767b31a4f94e7df7be\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4bdc0673852c12abb2fd5016c74b81\transformed\camera-video-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3cd0c8444a8be61ee325205569596b3\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7787d1ed458493f64ed12d5d72477b7\transformed\camera-core-1.3.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82baa44f9f0efab8e9269be8e7b691cd\transformed\camera-view-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f45fb687919ac6068b3495ea04d1e4c\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec7b75c409fae5c6e754c810938a9a33\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce079ded204864f26a45e70d5bf41b78\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc8266ca8ba43cd1000e107fd6537138\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:vision-common:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ec3a4b3cfcbc8b6ee37d1fd668aaa9\transformed\vision-common-17.2.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f08e0d719849b14ed4ab8905bd80cf7\transformed\play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.mlkit:vision-interfaces:16.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb5b44e895cdad6a0921b572dfbf7f3b\transformed\vision-interfaces-16.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dbc536f650649ade0e64fe4ea848936\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\949294b35df779989b974dc81fe3e123\transformed\play-services-basement-18.1.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9216b2a683b6cc5469c78f3c8b5e2ac\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2c8216ba33127d0f91e5c478cad333a\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce0c3990081a248f6ccf8301b854f008\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69e3ff8b24424e4b054198acbead4b7c\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90af2180f3c507755a6a40a2fcd0e417\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3659c750d2e7241c48e9d0fb2d025c19\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d68bc86f32b54e9d000deb9c917a49e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7eb0c54a5daffc47b62af4564d82940\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0104843c954d6992127fb14435a1afac\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c0cf13c09cd22e04a6fb3706baa728d\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4436a434712594a45ffba413a601369\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\666a19407a4267cd92a08d0f51cd9c94\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e641ca1d69cfcde0bfb2bf7fe2e1447\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0ad2569251c4acc1e2b0346a478b627\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b9bd70da48457a32f34d6a4add56547a\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f0b0eebd6804af9807f33dd16dc028d\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f3932efb1b4fcb8d9a33bf6e67bb5ce7\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be924f631c9c1453291cf5f7fe3f2f51\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01604262e65f72c6478a90da6f54f2c2\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c343f3bfc7ec0faad79f05799bd4e92b\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97ca4b5b666b86b7da52ed336294a214\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8182b7e5a700c215116fabf7fbbd8e43\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba120cb7a03c5efc724f7d33315bdba\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e267e193abcfbb9fbd21718191a697a2\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\199d223243bae7743a34127dbb74e037\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0172a29396cd11a3c903ea810e18ca06\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63b4fbf1851294ea4d9b2d883e2981ee\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5fd59763e27622519875fb320185594a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5bc15aa2430c1e427478222a8a471ee\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:15:1-38:12
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\898cf735b03ed440ea0f75123b8ae385\transformed\transport-api-2.2.1\AndroidManifest.xml:15:1-22:12
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b3cd5c1e7b1dfc505849a325f662390\transformed\firebase-components-16.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d960f14e7a36ba6b5aa0b4984ac43b5\transformed\firebase-encoders-json-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30c8ca72c852a78eb3d8a517cf81cfd5\transformed\exifinterface-1.3.2\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35945238fe273791c12d86471936a534\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad2198436865629f6d974154e92ec496\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87c8e4056f7850b1b370d03ddf491739\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\299721ee95bf679677c7bf787b238131\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef4415b3ce572af1a30fcd845e673ad7\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b1d371695adeab981c0404fd15e5e4ee\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96a7bd6e39c229975876d95384e4f437\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c972bb646f71788883c64c9a5f4e758\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12901804426a1c8136e7ba78d7fe988a\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f4afee320d17e09046f9de555c65b34\transformed\image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:2:11-69
uses-feature#android.hardware.camera.any
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:6:5-64
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:6:19-61
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:7:5-65
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:7:22-62
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:10:5-78
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:10:22-75
uses-permission#android.permission.BIND_ACCESSIBILITY_SERVICE
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:13:5-85
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:13:22-82
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:16:5-77
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:16:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_CAMERA
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:17:5-84
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:17:22-81
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:18:5-68
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:18:22-65
uses-permission#android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:21:5-95
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:21:22-92
uses-permission#android.permission.DISABLE_KEYGUARD
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:22:5-75
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:22:22-72
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:23:5-81
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:23:22-78
uses-permission#android.permission.SCHEDULE_EXACT_ALARM
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:26:5-79
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:26:22-76
uses-permission#android.permission.USE_EXACT_ALARM
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:27:5-74
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:27:22-71
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:28:5-77
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:28:22-74
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:29:5-66
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:29:22-63
application
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:31:5-115:19
INJECTED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:31:5-115:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06fd2fa39dd57ce426fb9e8f08397def\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06fd2fa39dd57ce426fb9e8f08397def\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4fa19f2f35e2767b31a4f94e7df7be\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4fa19f2f35e2767b31a4f94e7df7be\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7787d1ed458493f64ed12d5d72477b7\transformed\camera-core-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7787d1ed458493f64ed12d5d72477b7\transformed\camera-core-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc8266ca8ba43cd1000e107fd6537138\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc8266ca8ba43cd1000e107fd6537138\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ec3a4b3cfcbc8b6ee37d1fd668aaa9\transformed\vision-common-17.2.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ec3a4b3cfcbc8b6ee37d1fd668aaa9\transformed\vision-common-17.2.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f08e0d719849b14ed4ab8905bd80cf7\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f08e0d719849b14ed4ab8905bd80cf7\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dbc536f650649ade0e64fe4ea848936\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dbc536f650649ade0e64fe4ea848936\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\949294b35df779989b974dc81fe3e123\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\949294b35df779989b974dc81fe3e123\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d68bc86f32b54e9d000deb9c917a49e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d68bc86f32b54e9d000deb9c917a49e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:25:5-39:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad2198436865629f6d974154e92ec496\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad2198436865629f6d974154e92ec496\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\299721ee95bf679677c7bf787b238131\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\299721ee95bf679677c7bf787b238131\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f4afee320d17e09046f9de555c65b34\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f4afee320d17e09046f9de555c65b34\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:38:9-35
	android:label
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:36:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:34:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:37:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:40:9-29
	android:icon
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:35:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:32:9-35
	android:theme
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:39:9-51
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:33:9-65
meta-data#com.google.mlkit.vision.DEPENDENCIES
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:43:9-45:36
	android:value
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:45:13-33
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:44:13-64
activity#com.motasem.visualpointer.MainActivity
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:48:9-56:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:51:13-49
	android:exported
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:50:13-36
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:49:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:52:13-55:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:53:17-69
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:53:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:54:17-77
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:54:27-74
activity#com.motasem.visualpointer.ui.SettingsActivity
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:59:9-62:52
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:62:13-49
	android:exported
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:61:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:60:13-48
activity#com.motasem.visualpointer.ui.CalibrationActivity
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:65:9-68:52
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:68:13-49
	android:exported
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:67:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:66:13-51
activity#com.motasem.visualpointer.ui.AboutActivity
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:71:9-75:58
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:74:13-49
	android:exported
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:73:13-37
	android:theme
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:75:13-55
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:72:13-45
service#com.motasem.visualpointer.service.VisualPointerAccessibilityService
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:78:9-88:19
	android:exported
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:80:13-37
	android:permission
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:81:13-79
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:79:13-70
intent-filter#action:name:android.accessibilityservice.AccessibilityService
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:82:13-84:29
action#android.accessibilityservice.AccessibilityService
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:83:17-92
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:83:25-89
meta-data#android.accessibilityservice
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:85:13-87:72
	android:resource
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:87:17-69
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:86:17-60
service#com.motasem.visualpointer.service.EyeTrackingService
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:91:9-99:19
	android:enabled
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:94:13-35
	android:exported
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:93:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:95:13-62
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:92:13-55
property#android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:96:13-98:48
	android:value
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:98:17-45
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:97:17-76
receiver#com.motasem.visualpointer.service.ServiceRestartReceiver
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:102:9-113:20
	android:enabled
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:105:13-35
	android:exported
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:104:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:103:13-59
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.PACKAGE_REPLACED+action:name:com.motasem.visualpointer.RESTART_SERVICE+data:scheme:package
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:106:13-112:29
	android:priority
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:106:28-51
action#android.intent.action.BOOT_COMPLETED
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:107:17-79
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:107:25-76
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:108:17-84
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:108:25-81
action#android.intent.action.PACKAGE_REPLACED
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:109:17-81
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:109:25-78
action#com.motasem.visualpointer.RESTART_SERVICE
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:110:17-84
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:110:25-81
data
ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:111:17-50
	android:scheme
		ADDED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:111:23-47
uses-sdk
INJECTED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3c4d7dce088827d4a62951b6df06cd6\transformed\viewbinding-8.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3c4d7dce088827d4a62951b6df06cd6\transformed\viewbinding-8.8.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06fd2fa39dd57ce426fb9e8f08397def\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06fd2fa39dd57ce426fb9e8f08397def\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4fa19f2f35e2767b31a4f94e7df7be\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4fa19f2f35e2767b31a4f94e7df7be\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4bdc0673852c12abb2fd5016c74b81\transformed\camera-video-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4bdc0673852c12abb2fd5016c74b81\transformed\camera-video-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3cd0c8444a8be61ee325205569596b3\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3cd0c8444a8be61ee325205569596b3\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7787d1ed458493f64ed12d5d72477b7\transformed\camera-core-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7787d1ed458493f64ed12d5d72477b7\transformed\camera-core-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82baa44f9f0efab8e9269be8e7b691cd\transformed\camera-view-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82baa44f9f0efab8e9269be8e7b691cd\transformed\camera-view-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f45fb687919ac6068b3495ea04d1e4c\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f45fb687919ac6068b3495ea04d1e4c\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec7b75c409fae5c6e754c810938a9a33\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec7b75c409fae5c6e754c810938a9a33\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce079ded204864f26a45e70d5bf41b78\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce079ded204864f26a45e70d5bf41b78\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc8266ca8ba43cd1000e107fd6537138\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc8266ca8ba43cd1000e107fd6537138\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ec3a4b3cfcbc8b6ee37d1fd668aaa9\transformed\vision-common-17.2.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ec3a4b3cfcbc8b6ee37d1fd668aaa9\transformed\vision-common-17.2.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f08e0d719849b14ed4ab8905bd80cf7\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f08e0d719849b14ed4ab8905bd80cf7\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.mlkit:vision-interfaces:16.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb5b44e895cdad6a0921b572dfbf7f3b\transformed\vision-interfaces-16.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb5b44e895cdad6a0921b572dfbf7f3b\transformed\vision-interfaces-16.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dbc536f650649ade0e64fe4ea848936\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dbc536f650649ade0e64fe4ea848936\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\949294b35df779989b974dc81fe3e123\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\949294b35df779989b974dc81fe3e123\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9216b2a683b6cc5469c78f3c8b5e2ac\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9216b2a683b6cc5469c78f3c8b5e2ac\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2c8216ba33127d0f91e5c478cad333a\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2c8216ba33127d0f91e5c478cad333a\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce0c3990081a248f6ccf8301b854f008\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce0c3990081a248f6ccf8301b854f008\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69e3ff8b24424e4b054198acbead4b7c\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69e3ff8b24424e4b054198acbead4b7c\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90af2180f3c507755a6a40a2fcd0e417\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90af2180f3c507755a6a40a2fcd0e417\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3659c750d2e7241c48e9d0fb2d025c19\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3659c750d2e7241c48e9d0fb2d025c19\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d68bc86f32b54e9d000deb9c917a49e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d68bc86f32b54e9d000deb9c917a49e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7eb0c54a5daffc47b62af4564d82940\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7eb0c54a5daffc47b62af4564d82940\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0104843c954d6992127fb14435a1afac\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0104843c954d6992127fb14435a1afac\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c0cf13c09cd22e04a6fb3706baa728d\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c0cf13c09cd22e04a6fb3706baa728d\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4436a434712594a45ffba413a601369\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4436a434712594a45ffba413a601369\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\666a19407a4267cd92a08d0f51cd9c94\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\666a19407a4267cd92a08d0f51cd9c94\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e641ca1d69cfcde0bfb2bf7fe2e1447\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e641ca1d69cfcde0bfb2bf7fe2e1447\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0ad2569251c4acc1e2b0346a478b627\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0ad2569251c4acc1e2b0346a478b627\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b9bd70da48457a32f34d6a4add56547a\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b9bd70da48457a32f34d6a4add56547a\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f0b0eebd6804af9807f33dd16dc028d\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f0b0eebd6804af9807f33dd16dc028d\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f3932efb1b4fcb8d9a33bf6e67bb5ce7\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f3932efb1b4fcb8d9a33bf6e67bb5ce7\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be924f631c9c1453291cf5f7fe3f2f51\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be924f631c9c1453291cf5f7fe3f2f51\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01604262e65f72c6478a90da6f54f2c2\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01604262e65f72c6478a90da6f54f2c2\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c343f3bfc7ec0faad79f05799bd4e92b\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c343f3bfc7ec0faad79f05799bd4e92b\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97ca4b5b666b86b7da52ed336294a214\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97ca4b5b666b86b7da52ed336294a214\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8182b7e5a700c215116fabf7fbbd8e43\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8182b7e5a700c215116fabf7fbbd8e43\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba120cb7a03c5efc724f7d33315bdba\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba120cb7a03c5efc724f7d33315bdba\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e267e193abcfbb9fbd21718191a697a2\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e267e193abcfbb9fbd21718191a697a2\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\199d223243bae7743a34127dbb74e037\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\199d223243bae7743a34127dbb74e037\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0172a29396cd11a3c903ea810e18ca06\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0172a29396cd11a3c903ea810e18ca06\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63b4fbf1851294ea4d9b2d883e2981ee\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63b4fbf1851294ea4d9b2d883e2981ee\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5fd59763e27622519875fb320185594a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5fd59763e27622519875fb320185594a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5bc15aa2430c1e427478222a8a471ee\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5bc15aa2430c1e427478222a8a471ee\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\898cf735b03ed440ea0f75123b8ae385\transformed\transport-api-2.2.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\898cf735b03ed440ea0f75123b8ae385\transformed\transport-api-2.2.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b3cd5c1e7b1dfc505849a325f662390\transformed\firebase-components-16.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b3cd5c1e7b1dfc505849a325f662390\transformed\firebase-components-16.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d960f14e7a36ba6b5aa0b4984ac43b5\transformed\firebase-encoders-json-17.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d960f14e7a36ba6b5aa0b4984ac43b5\transformed\firebase-encoders-json-17.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30c8ca72c852a78eb3d8a517cf81cfd5\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30c8ca72c852a78eb3d8a517cf81cfd5\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35945238fe273791c12d86471936a534\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35945238fe273791c12d86471936a534\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad2198436865629f6d974154e92ec496\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad2198436865629f6d974154e92ec496\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87c8e4056f7850b1b370d03ddf491739\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87c8e4056f7850b1b370d03ddf491739\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\299721ee95bf679677c7bf787b238131\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\299721ee95bf679677c7bf787b238131\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef4415b3ce572af1a30fcd845e673ad7\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef4415b3ce572af1a30fcd845e673ad7\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b1d371695adeab981c0404fd15e5e4ee\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b1d371695adeab981c0404fd15e5e4ee\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96a7bd6e39c229975876d95384e4f437\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96a7bd6e39c229975876d95384e4f437\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c972bb646f71788883c64c9a5f4e758\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c972bb646f71788883c64c9a5f4e758\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12901804426a1c8136e7ba78d7fe988a\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12901804426a1c8136e7ba78d7fe988a\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f4afee320d17e09046f9de555c65b34\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f4afee320d17e09046f9de555c65b34\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7787d1ed458493f64ed12d5d72477b7\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7787d1ed458493f64ed12d5d72477b7\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc8266ca8ba43cd1000e107fd6537138\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ec3a4b3cfcbc8b6ee37d1fd668aaa9\transformed\vision-common-17.2.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ec3a4b3cfcbc8b6ee37d1fd668aaa9\transformed\vision-common-17.2.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc8266ca8ba43cd1000e107fd6537138\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc8266ca8ba43cd1000e107fd6537138\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:10:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.face.internal.FaceRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc8266ca8ba43cd1000e107fd6537138\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc8266ca8ba43cd1000e107fd6537138\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc8266ca8ba43cd1000e107fd6537138\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:13:17-114
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ec3a4b3cfcbc8b6ee37d1fd668aaa9\transformed\vision-common-17.2.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ec3a4b3cfcbc8b6ee37d1fd668aaa9\transformed\vision-common-17.2.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ec3a4b3cfcbc8b6ee37d1fd668aaa9\transformed\vision-common-17.2.0\AndroidManifest.xml:13:17-124
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:21:17-120
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f08e0d719849b14ed4ab8905bd80cf7\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f08e0d719849b14ed4ab8905bd80cf7\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f08e0d719849b14ed4ab8905bd80cf7\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f08e0d719849b14ed4ab8905bd80cf7\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\949294b35df779989b974dc81fe3e123\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\949294b35df779989b974dc81fe3e123\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\949294b35df779989b974dc81fe3e123\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d68bc86f32b54e9d000deb9c917a49e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d68bc86f32b54e9d000deb9c917a49e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\299721ee95bf679677c7bf787b238131\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\299721ee95bf679677c7bf787b238131\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d68bc86f32b54e9d000deb9c917a49e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d68bc86f32b54e9d000deb9c917a49e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d68bc86f32b54e9d000deb9c917a49e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.motasem.visualpointer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.motasem.visualpointer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:22-76
uses-permission#android.permission.INTERNET
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:22-64
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
