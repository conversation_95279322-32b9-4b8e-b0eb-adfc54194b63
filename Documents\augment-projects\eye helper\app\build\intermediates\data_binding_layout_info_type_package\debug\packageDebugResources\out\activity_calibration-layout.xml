<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_calibration" modulePackage="com.motasem.visualpointer" filePath="app\src\main\res\layout\activity_calibration.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.FrameLayout"><Targets><Target tag="layout/activity_calibration_0" view="FrameLayout"><Expressions/><location startLine="1" startOffset="0" endLine="127" endOffset="13"/></Target><Target id="@+id/calibrationPreviewView" view="androidx.camera.view.PreviewView"><Expressions/><location startLine="8" startOffset="4" endLine="12" endOffset="36"/></Target><Target id="@+id/calibrationGraphicOverlay" view="com.motasem.visualpointer.ui.GraphicOverlay"><Expressions/><location startLine="15" startOffset="4" endLine="18" endOffset="46"/></Target><Target id="@+id/calibrationPointsContainer" view="FrameLayout"><Expressions/><location startLine="21" startOffset="4" endLine="35" endOffset="17"/></Target><Target id="@+id/calibrationPoint" view="View"><Expressions/><location startLine="27" startOffset="8" endLine="33" endOffset="39"/></Target><Target id="@+id/instructionsContainer" view="LinearLayout"><Expressions/><location startLine="38" startOffset="4" endLine="78" endOffset="18"/></Target><Target id="@+id/calibrationTitle" view="TextView"><Expressions/><location startLine="48" startOffset="8" endLine="56" endOffset="38"/></Target><Target id="@+id/calibrationInstruction" view="TextView"><Expressions/><location startLine="58" startOffset="8" endLine="66" endOffset="37"/></Target><Target id="@+id/calibrationProgress" view="TextView"><Expressions/><location startLine="68" startOffset="8" endLine="76" endOffset="37"/></Target><Target id="@+id/controlButtonsContainer" view="LinearLayout"><Expressions/><location startLine="81" startOffset="4" endLine="110" endOffset="18"/></Target><Target id="@+id/startCalibrationButton" view="Button"><Expressions/><location startLine="89" startOffset="8" endLine="94" endOffset="41"/></Target><Target id="@+id/skipCalibrationButton" view="Button"><Expressions/><location startLine="96" startOffset="8" endLine="101" endOffset="33"/></Target><Target id="@+id/backFromCalibrationButton" view="Button"><Expressions/><location startLine="103" startOffset="8" endLine="108" endOffset="33"/></Target><Target id="@+id/calibrationProgressBar" view="ProgressBar"><Expressions/><location startLine="113" startOffset="4" endLine="125" endOffset="35"/></Target></Targets></Layout>