package com.motasem.visualpointer.tracking

import android.graphics.PointF
import android.util.Log
import com.google.mlkit.vision.face.Face
import com.google.mlkit.vision.face.FaceLandmark
import com.motasem.visualpointer.model.EyeTrackingData
import com.motasem.visualpointer.utils.PreferencesManager
import kotlin.math.abs
import kotlin.math.sqrt

/**
 * Advanced face tracking implementation using nose and mouth for better accuracy
 */
class EyeTracker(private val preferencesManager: PreferencesManager) {

    companion object {
        private const val TAG = "EyeTracker"
        private const val SMOOTHING_FACTOR = 0.1f // تنعيم أقل للاستجابة السريعة
        private const val MIN_CONFIDENCE_THRESHOLD = 0.3f // عتبة أقل للحصول على تتبع أفضل
        private const val ADAPTIVE_SMOOTHING_MIN = 0.05f // تنعيم أقل جداً للاستجابة الفورية
        private const val ADAPTIVE_SMOOTHING_MAX = 0.2f // تنعيم أقل للحركة البطيئة
        private const val MOVEMENT_THRESHOLD = 15f // عتبة أقل للحساسية العالية
        private const val HEAD_ROTATION_SMOOTHING = 0.05f // تنعيم أقل لدوران الرأس للاستجابة السريعة

        // أنواع التتبع المختلفة
        enum class TrackingMode {
            NOSE_3D,    // تتبع الأنف ثلاثي الأبعاد (الافتراضي)
            MOUTH_3D,   // تتبع الفم ثلاثي الأبعاد
            EYE_CENTER, // تتبع مركز العينين (التقليدي)
            FACE_CENTER // تتبع مركز الوجه
        }
    }

    private var lastGazePoint = PointF(0f, 0f)
    private var gazeHistory = mutableListOf<PointF>()

    // متغيرات لحفظ دوران الرأس السابق للتنعيم 3D
    private var lastHeadRotationX = 0f
    private var lastHeadRotationY = 0f
    private var lastHeadRotationZ = 0f

    // نوع التتبع الحالي
    private var currentTrackingMode = TrackingMode.NOSE_3D

    // متغيرات للمعايرة والمسافة
    private var calibrationCenterX = 0f
    private var calibrationCenterY = 0f
    private var isCalibrated = false
    private var lastFaceDistance = 0f
    private val maxHistorySize = 5

    /**
     * Process face detection result with multiple tracking modes
     */
    fun processFrame(face: Face, screenWidth: Int, screenHeight: Int): EyeTrackingData? {
        try {
            // فحص توفر النقاط المطلوبة حسب نوع التتبع
            if (!hasRequiredLandmarks(face)) {
                Log.w(TAG, "Required landmarks not detected for tracking mode: $currentTrackingMode")
                return null
            }

            // حساب المسافة من الكاميرا لتحسين الدقة
            val faceDistance = calculateFaceDistance(face)

            // Calculate gaze point using selected tracking mode
            val gazePoint = when (currentTrackingMode) {
                TrackingMode.NOSE_3D -> calculateNose3DTracking(face, screenWidth, screenHeight, faceDistance)
                TrackingMode.MOUTH_3D -> calculateMouth3DTracking(face, screenWidth, screenHeight, faceDistance)
                TrackingMode.EYE_CENTER -> calculateEyeCenterTracking(face, screenWidth, screenHeight, faceDistance)
                TrackingMode.FACE_CENTER -> calculateFaceCenterTracking(face, screenWidth, screenHeight, faceDistance)
            }

            // Apply advanced smoothing based on movement speed
            val smoothedGaze = applySmoothingFilter(gazePoint)

            // Update history
            updateGazeHistory(smoothedGaze)

            // تحديث المسافة الأخيرة
            lastFaceDistance = faceDistance

            Log.d(TAG, "Tracking mode: $currentTrackingMode, Distance: $faceDistance, Point: (${smoothedGaze.x}, ${smoothedGaze.y})")

            return EyeTrackingData(
                gazeX = smoothedGaze.x,
                gazeY = smoothedGaze.y,
                leftEyeOpenProbability = face.leftEyeOpenProbability ?: 1f,
                rightEyeOpenProbability = face.rightEyeOpenProbability ?: 1f,
                faceDetected = true
            )

        } catch (e: Exception) {
            Log.e(TAG, "Error processing frame", e)
            return null
        }
    }

    /**
     * Check if required landmarks are available for current tracking mode
     */
    private fun hasRequiredLandmarks(face: Face): Boolean {
        return when (currentTrackingMode) {
            TrackingMode.NOSE_3D -> face.getLandmark(FaceLandmark.NOSE_BASE) != null
            TrackingMode.MOUTH_3D -> {
                face.getLandmark(FaceLandmark.MOUTH_LEFT) != null &&
                face.getLandmark(FaceLandmark.MOUTH_RIGHT) != null
            }
            TrackingMode.EYE_CENTER -> {
                face.getLandmark(FaceLandmark.LEFT_EYE) != null &&
                face.getLandmark(FaceLandmark.RIGHT_EYE) != null
            }
            TrackingMode.FACE_CENTER -> true // Always available
        }
    }

    /**
     * Calculate face distance from camera for better accuracy
     */
    private fun calculateFaceDistance(face: Face): Float {
        val faceBox = face.boundingBox
        val faceWidth = faceBox.width().toFloat()

        // تقدير المسافة بناءً على عرض الوجه
        // كلما كان الوجه أكبر، كلما كان أقرب
        val normalizedDistance = 300f / faceWidth // قيمة مرجعية
        return normalizedDistance.coerceIn(0.5f, 3.0f)
    }

    /**
     * Nose 3D tracking - most accurate method
     */
    private fun calculateNose3DTracking(
        face: Face,
        screenWidth: Int,
        screenHeight: Int,
        faceDistance: Float
    ): PointF {
        val noseTip = face.getLandmark(FaceLandmark.NOSE_BASE)!!
        return calculate3DHeadTracking(noseTip.position, face, screenWidth, screenHeight, faceDistance)
    }

    /**
     * Mouth 3D tracking - alternative method
     */
    private fun calculateMouth3DTracking(
        face: Face,
        screenWidth: Int,
        screenHeight: Int,
        faceDistance: Float
    ): PointF {
        val mouthLeft = face.getLandmark(FaceLandmark.MOUTH_LEFT)!!
        val mouthRight = face.getLandmark(FaceLandmark.MOUTH_RIGHT)!!

        val mouthCenter = PointF(
            (mouthLeft.position.x + mouthRight.position.x) / 2f,
            (mouthLeft.position.y + mouthRight.position.y) / 2f
        )

        return calculate3DHeadTracking(mouthCenter, face, screenWidth, screenHeight, faceDistance)
    }

    /**
     * Eye center tracking - traditional method
     */
    private fun calculateEyeCenterTracking(
        face: Face,
        screenWidth: Int,
        screenHeight: Int,
        faceDistance: Float
    ): PointF {
        val leftEye = face.getLandmark(FaceLandmark.LEFT_EYE)!!
        val rightEye = face.getLandmark(FaceLandmark.RIGHT_EYE)!!

        val eyeCenter = PointF(
            (leftEye.position.x + rightEye.position.x) / 2f,
            (leftEye.position.y + rightEye.position.y) / 2f
        )

        return calculate3DHeadTracking(eyeCenter, face, screenWidth, screenHeight, faceDistance)
    }

    /**
     * Face center tracking - fallback method
     */
    private fun calculateFaceCenterTracking(
        face: Face,
        screenWidth: Int,
        screenHeight: Int,
        faceDistance: Float
    ): PointF {
        val faceBox = face.boundingBox
        val faceCenter = PointF(faceBox.centerX().toFloat(), faceBox.centerY().toFloat())

        return calculate3DHeadTracking(faceCenter, face, screenWidth, screenHeight, faceDistance)
    }

    /**
     * Calculate 3D head tracking - Enhanced with distance compensation
     * User just looks around, no need to move nose/mouth physically
     */
    private fun calculate3DHeadTracking(
        referencePoint: PointF,
        face: Face,
        screenWidth: Int,
        screenHeight: Int,
        faceDistance: Float
    ): PointF {

        // الحصول على زوايا دوران الرأس 3D
        val rawHeadRotationY = face.headEulerAngleY // يمين/يسار (-90 إلى +90)
        val rawHeadRotationX = face.headEulerAngleX // أعلى/أسفل (-90 إلى +90)
        val rawHeadRotationZ = face.headEulerAngleZ // ميل (-180 إلى +180)

        // تطبيق تنعيم على دوران الرأس للحصول على حركة أكثر سلاسة
        val headRotationY = if (lastHeadRotationY == 0f) {
            rawHeadRotationY
        } else {
            lastHeadRotationY * (1 - HEAD_ROTATION_SMOOTHING) + rawHeadRotationY * HEAD_ROTATION_SMOOTHING
        }

        val headRotationX = if (lastHeadRotationX == 0f) {
            rawHeadRotationX
        } else {
            lastHeadRotationX * (1 - HEAD_ROTATION_SMOOTHING) + rawHeadRotationX * HEAD_ROTATION_SMOOTHING
        }

        val headRotationZ = if (lastHeadRotationZ == 0f) {
            rawHeadRotationZ
        } else {
            lastHeadRotationZ * (1 - HEAD_ROTATION_SMOOTHING) + rawHeadRotationZ * HEAD_ROTATION_SMOOTHING
        }

        // حفظ القيم للمرة القادمة
        lastHeadRotationX = headRotationX
        lastHeadRotationY = headRotationY
        lastHeadRotationZ = headRotationZ

        // حساب موقع المؤشر بناءً على دوران الرأس مع تعويض المسافة
        val centerX = if (isCalibrated) calibrationCenterX else screenWidth / 2f
        val centerY = if (isCalibrated) calibrationCenterY else screenHeight / 2f

        // الحساسية المحسنة للحركة 3D مع تعويض المسافة
        val baseSensitivity = preferencesManager.getEyeTrackingSensitivity() / 100f
        val distanceCompensation = 1f / faceDistance.coerceIn(0.5f, 2.0f) // تحديد نطاق المسافة
        val enhancedSensitivity = (baseSensitivity * 8.0f * distanceCompensation).coerceIn(1.0f, 15.0f) // حساسية عالية جداً

        // تحويل زوايا الدوران إلى إحداثيات الشاشة مع إصلاح الاتجاهات
        // FIXED: كلما دار الرأس يميناً (+Y)، المؤشر يتحرك يميناً (نفس الاتجاه)
        val deltaX = headRotationY * enhancedSensitivity * (screenWidth / 30f) // حساسية مضاعفة

        // FIXED: كلما دار الرأس لأسفل (+X)، المؤشر يتحرك لأسفل (نفس الاتجاه)
        val deltaY = headRotationX * enhancedSensitivity * (screenHeight / 30f) // حساسية مضاعفة

        // حساب الموقع النهائي
        var finalX = centerX + deltaX
        var finalY = centerY + deltaY

        // تطبيق تعديل بناءً على ميل الرأس (Z rotation) للدقة الإضافية
        val tiltAdjustment = headRotationZ * 0.05f * enhancedSensitivity
        finalX += tiltAdjustment

        // تطبيق حدود الشاشة مع هامش ديناميكي
        val margin = 30f
        finalX = finalX.coerceIn(margin, screenWidth.toFloat() - margin)
        finalY = finalY.coerceIn(margin, screenHeight.toFloat() - margin)

        Log.d(TAG, "3D Tracking - Mode: $currentTrackingMode, Distance: $faceDistance, " +
                "Rotation: ($headRotationX, $headRotationY), Result: ($finalX, $finalY)")

        return PointF(finalX, finalY)
    }



    /**
     * Apply curve mapping for better accuracy at screen edges
     */
    private fun applyCurveMapping(value: Float): Float {
        // تطبيق منحنى S-curve لتحسين الدقة
        val centered = (value - 0.5f) * 2f // تحويل إلى نطاق -1 إلى 1
        val curved = centered * abs(centered) // منحنى تربيعي
        return (curved / 2f) + 0.5f // إعادة إلى نطاق 0 إلى 1
    }

    /**
     * Apply calibration data to normalize gaze coordinates with enhanced accuracy
     */
    private fun applyCalibration(normalizedX: Float, normalizedY: Float): PointF {
        val calibrationData = preferencesManager.getCalibrationData()

        if (calibrationData.isNullOrEmpty()) {
            // No calibration data, apply default adjustments for better accuracy
            val adjustedX = enhanceEdgeAccuracy(normalizedX)
            val adjustedY = enhanceEdgeAccuracy(normalizedY)
            return PointF(adjustedX, adjustedY)
        }

        // Parse calibration data and apply transformation
        // This is a more sophisticated implementation
        try {
            val calibrationPoints = parseCalibrationData(calibrationData)
            if (calibrationPoints.size >= 4) {
                // استخدام interpolation بين نقاط المعايرة
                val interpolatedPoint = interpolateCalibration(normalizedX, normalizedY, calibrationPoints)
                return PointF(
                    interpolatedPoint.x.coerceIn(0f, 1f),
                    interpolatedPoint.y.coerceIn(0f, 1f)
                )
            }
        } catch (e: Exception) {
            Log.w(TAG, "Error applying calibration data", e)
        }

        // Fallback to enhanced mapping
        val adjustedX = enhanceEdgeAccuracy(normalizedX)
        val adjustedY = enhanceEdgeAccuracy(normalizedY)
        return PointF(adjustedX, adjustedY)
    }

    /**
     * Enhance accuracy at screen edges
     */
    private fun enhanceEdgeAccuracy(value: Float): Float {
        // تحسين الدقة في أطراف الشاشة
        val edgeThreshold = 0.1f
        val edgeBoost = 0.15f

        return when {
            value < edgeThreshold -> {
                // تعزيز الحركة في الطرف الأيسر/العلوي
                value * (1f + edgeBoost)
            }
            value > (1f - edgeThreshold) -> {
                // تعزيز الحركة في الطرف الأيمن/السفلي
                1f - ((1f - value) * (1f + edgeBoost))
            }
            else -> value
        }
    }

    /**
     * Parse calibration data from preferences
     */
    private fun parseCalibrationData(data: String): List<PointF> {
        // تحليل بيانات المعايرة من النص المحفوظ
        val points = mutableListOf<PointF>()
        try {
            val lines = data.split("\n")
            for (line in lines) {
                if (line.contains(",")) {
                    val coords = line.split(",")
                    if (coords.size >= 2) {
                        val x = coords[0].toFloat()
                        val y = coords[1].toFloat()
                        points.add(PointF(x, y))
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing calibration data", e)
        }
        return points
    }

    /**
     * Interpolate between calibration points
     */
    private fun interpolateCalibration(x: Float, y: Float, calibrationPoints: List<PointF>): PointF {
        // استخدام bilinear interpolation بين نقاط المعايرة
        // هذا تطبيق مبسط - في التطبيق الحقيقي نحتاج خوارزمية أكثر تعقيداً

        if (calibrationPoints.size < 4) {
            return PointF(x, y)
        }

        // العثور على أقرب نقاط المعايرة
        var closestDistance = Float.MAX_VALUE
        var closestPoint = calibrationPoints[0]

        for (point in calibrationPoints) {
            val distance = sqrt((x - point.x) * (x - point.x) + (y - point.y) * (y - point.y))
            if (distance < closestDistance) {
                closestDistance = distance
                closestPoint = point
            }
        }

        // تطبيق تعديل بسيط بناءً على أقرب نقطة
        val adjustmentFactor = 0.1f
        val adjustedX = x + (closestPoint.x - x) * adjustmentFactor
        val adjustedY = y + (closestPoint.y - y) * adjustmentFactor

        return PointF(adjustedX, adjustedY)
    }

    /**
     * Apply adaptive smoothing filter to reduce jitter while maintaining responsiveness
     */
    private fun applySmoothingFilter(newPoint: PointF): PointF {
        if (lastGazePoint.x == 0f && lastGazePoint.y == 0f) {
            lastGazePoint = newPoint
            return newPoint
        }

        // حساب المسافة بين النقطة الجديدة والسابقة
        val distance = sqrt(
            (newPoint.x - lastGazePoint.x) * (newPoint.x - lastGazePoint.x) +
            (newPoint.y - lastGazePoint.y) * (newPoint.y - lastGazePoint.y)
        )

        // تطبيق تنعيم تكيفي بناءً على سرعة الحركة
        val adaptiveSmoothingFactor = when {
            distance < MOVEMENT_THRESHOLD -> {
                // حركة بطيئة - تنعيم أكثر لتقليل الاهتزاز
                ADAPTIVE_SMOOTHING_MAX
            }
            distance > MOVEMENT_THRESHOLD * 3 -> {
                // حركة سريعة - تنعيم أقل للاستجابة السريعة
                ADAPTIVE_SMOOTHING_MIN
            }
            else -> {
                // حركة متوسطة - تنعيم متوسط
                SMOOTHING_FACTOR
            }
        }

        // تطبيق التنعيم مع الأخذ في الاعتبار تاريخ النقاط
        val smoothedX = if (gazeHistory.size >= 3) {
            // استخدام متوسط مرجح للنقاط الثلاث الأخيرة
            val weight1 = 0.5f // النقطة الحالية
            val weight2 = 0.3f // النقطة السابقة
            val weight3 = 0.2f // النقطة قبل السابقة

            newPoint.x * weight1 +
            lastGazePoint.x * weight2 +
            gazeHistory[gazeHistory.size - 2].x * weight3
        } else {
            lastGazePoint.x * (1 - adaptiveSmoothingFactor) + newPoint.x * adaptiveSmoothingFactor
        }

        val smoothedY = if (gazeHistory.size >= 3) {
            val weight1 = 0.5f
            val weight2 = 0.3f
            val weight3 = 0.2f

            newPoint.y * weight1 +
            lastGazePoint.y * weight2 +
            gazeHistory[gazeHistory.size - 2].y * weight3
        } else {
            lastGazePoint.y * (1 - adaptiveSmoothingFactor) + newPoint.y * adaptiveSmoothingFactor
        }

        val smoothedPoint = PointF(smoothedX, smoothedY)
        lastGazePoint = smoothedPoint

        Log.d(TAG, "Distance: $distance, Smoothing: $adaptiveSmoothingFactor")

        return smoothedPoint
    }

    /**
     * Update gaze history for advanced filtering
     */
    private fun updateGazeHistory(point: PointF) {
        gazeHistory.add(point)
        if (gazeHistory.size > maxHistorySize) {
            gazeHistory.removeAt(0)
        }
    }

    /**
     * Get average gaze point from history
     */
    fun getAverageGazePoint(): PointF? {
        if (gazeHistory.isEmpty()) return null
        
        var sumX = 0f
        var sumY = 0f
        
        for (point in gazeHistory) {
            sumX += point.x
            sumY += point.y
        }
        
        return PointF(sumX / gazeHistory.size, sumY / gazeHistory.size)
    }

    /**
     * Calculate gaze stability (lower values = more stable)
     */
    fun getGazeStability(): Float {
        if (gazeHistory.size < 2) return 1f
        
        var totalDistance = 0f
        for (i in 1 until gazeHistory.size) {
            val prev = gazeHistory[i - 1]
            val curr = gazeHistory[i]
            val distance = sqrt((curr.x - prev.x) * (curr.x - prev.x) + (curr.y - prev.y) * (curr.y - prev.y))
            totalDistance += distance
        }
        
        return totalDistance / (gazeHistory.size - 1)
    }

    /**
     * Reset tracking state including 3D head rotation history
     */
    fun reset() {
        lastGazePoint = PointF(0f, 0f)
        gazeHistory.clear()

        // إعادة تعيين دوران الرأس
        lastHeadRotationX = 0f
        lastHeadRotationY = 0f
        lastHeadRotationZ = 0f

        // إعادة تعيين المسافة
        lastFaceDistance = 0f

        Log.d(TAG, "3D Head tracking reset for mode: $currentTrackingMode")
    }

    /**
     * Set tracking mode
     */
    fun setTrackingMode(mode: TrackingMode) {
        if (currentTrackingMode != mode) {
            currentTrackingMode = mode
            reset() // إعادة تعيين التتبع عند تغيير النوع
            Log.i(TAG, "Tracking mode changed to: $mode")
        }
    }

    /**
     * Get current tracking mode
     */
    fun getTrackingMode(): TrackingMode = currentTrackingMode

    /**
     * Calibrate tracking for current user position
     */
    fun calibrate(screenCenterX: Float, screenCenterY: Float) {
        calibrationCenterX = screenCenterX
        calibrationCenterY = screenCenterY
        isCalibrated = true
        Log.i(TAG, "Calibration set to: ($screenCenterX, $screenCenterY) for mode: $currentTrackingMode")
    }

    /**
     * Reset calibration
     */
    fun resetCalibration() {
        isCalibrated = false
        calibrationCenterX = 0f
        calibrationCenterY = 0f
        Log.i(TAG, "Calibration reset")
    }

    /**
     * Get available tracking modes as strings for UI
     */
    fun getAvailableTrackingModes(): List<String> {
        return listOf(
            "تتبع الأنف 3D (الأفضل)",
            "تتبع الفم 3D",
            "تتبع مركز العينين",
            "تتبع مركز الوجه"
        )
    }

    /**
     * Set tracking mode by index
     */
    fun setTrackingModeByIndex(index: Int) {
        val modes = TrackingMode.values()
        if (index in modes.indices) {
            setTrackingMode(modes[index])
        }
    }

    /**
     * Get current tracking mode index
     */
    fun getCurrentTrackingModeIndex(): Int {
        return TrackingMode.values().indexOf(currentTrackingMode)
    }

    /**
     * Check if gaze is stable enough for interaction
     */
    fun isGazeStable(): Boolean {
        return getGazeStability() < 50f // Threshold for stability
    }
}
