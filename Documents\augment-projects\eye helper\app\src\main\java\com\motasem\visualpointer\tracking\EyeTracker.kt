package com.motasem.visualpointer.tracking

import android.graphics.PointF
import android.util.Log
import com.google.mlkit.vision.face.Face
import com.google.mlkit.vision.face.FaceLandmark
import com.motasem.visualpointer.model.EyeTrackingData
import com.motasem.visualpointer.utils.PreferencesManager
import kotlin.math.abs
import kotlin.math.sqrt

/**
 * Advanced eye tracking implementation
 */
class EyeTracker(private val preferencesManager: PreferencesManager) {

    companion object {
        private const val TAG = "EyeTracker"
        private const val SMOOTHING_FACTOR = 0.3f
        private const val MIN_CONFIDENCE_THRESHOLD = 0.7f
    }

    private var lastGazePoint = PointF(0f, 0f)
    private var gazeHistory = mutableListOf<PointF>()
    private val maxHistorySize = 5

    /**
     * Process face detection result and extract eye tracking data
     */
    fun processFrame(face: Face, screenWidth: Int, screenHeight: Int): EyeTrackingData? {
        try {
            val leftEye = face.getLandmark(FaceLandmark.LEFT_EYE)
            val rightEye = face.getLandmark(FaceLandmark.RIGHT_EYE)
            
            if (leftEye == null || rightEye == null) {
                Log.w(TAG, "Eye landmarks not detected")
                return null
            }

            // Calculate gaze point
            val gazePoint = calculateGazePoint(leftEye, rightEye, face, screenWidth, screenHeight)
            
            // Apply smoothing
            val smoothedGaze = applySmoothingFilter(gazePoint)
            
            // Update history
            updateGazeHistory(smoothedGaze)
            
            return EyeTrackingData(
                gazeX = smoothedGaze.x,
                gazeY = smoothedGaze.y,
                leftEyeOpenProbability = face.leftEyeOpenProbability ?: 1f,
                rightEyeOpenProbability = face.rightEyeOpenProbability ?: 1f,
                faceDetected = true
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Error processing frame", e)
            return null
        }
    }

    /**
     * Calculate gaze point based on eye landmarks
     */
    private fun calculateGazePoint(
        leftEye: FaceLandmark,
        rightEye: FaceLandmark,
        face: Face,
        screenWidth: Int,
        screenHeight: Int
    ): PointF {
        
        // Get eye center points
        val leftEyeCenter = leftEye.position
        val rightEyeCenter = rightEye.position
        
        // Calculate the center point between eyes
        val eyeCenter = PointF(
            (leftEyeCenter.x + rightEyeCenter.x) / 2f,
            (leftEyeCenter.y + rightEyeCenter.y) / 2f
        )
        
        // Get face bounding box
        val faceBox = face.boundingBox
        
        // Normalize eye position relative to face
        val normalizedX = (eyeCenter.x - faceBox.left) / faceBox.width().toFloat()
        val normalizedY = (eyeCenter.y - faceBox.top) / faceBox.height().toFloat()
        
        // Apply calibration if available
        val calibratedPoint = applyCalibration(normalizedX, normalizedY)
        
        // Map to screen coordinates
        val screenX = calibratedPoint.x * screenWidth
        val screenY = calibratedPoint.y * screenHeight
        
        // Apply sensitivity settings
        val sensitivity = preferencesManager.getEyeTrackingSensitivity() / 100f
        val adjustedX = lastGazePoint.x + (screenX - lastGazePoint.x) * sensitivity
        val adjustedY = lastGazePoint.y + (screenY - lastGazePoint.y) * sensitivity
        
        return PointF(adjustedX, adjustedY)
    }

    /**
     * Apply calibration data to normalize gaze coordinates
     */
    private fun applyCalibration(normalizedX: Float, normalizedY: Float): PointF {
        val calibrationData = preferencesManager.getCalibrationData()
        
        if (calibrationData.isNullOrEmpty()) {
            // No calibration data, use direct mapping
            return PointF(normalizedX, normalizedY)
        }
        
        // Parse calibration data and apply transformation
        // This is a simplified implementation
        // In a real scenario, you would use more sophisticated calibration algorithms
        
        return PointF(
            normalizedX.coerceIn(0f, 1f),
            normalizedY.coerceIn(0f, 1f)
        )
    }

    /**
     * Apply smoothing filter to reduce jitter
     */
    private fun applySmoothingFilter(newPoint: PointF): PointF {
        if (lastGazePoint.x == 0f && lastGazePoint.y == 0f) {
            lastGazePoint = newPoint
            return newPoint
        }
        
        val smoothedX = lastGazePoint.x * (1 - SMOOTHING_FACTOR) + newPoint.x * SMOOTHING_FACTOR
        val smoothedY = lastGazePoint.y * (1 - SMOOTHING_FACTOR) + newPoint.y * SMOOTHING_FACTOR
        
        val smoothedPoint = PointF(smoothedX, smoothedY)
        lastGazePoint = smoothedPoint
        
        return smoothedPoint
    }

    /**
     * Update gaze history for advanced filtering
     */
    private fun updateGazeHistory(point: PointF) {
        gazeHistory.add(point)
        if (gazeHistory.size > maxHistorySize) {
            gazeHistory.removeAt(0)
        }
    }

    /**
     * Get average gaze point from history
     */
    fun getAverageGazePoint(): PointF? {
        if (gazeHistory.isEmpty()) return null
        
        var sumX = 0f
        var sumY = 0f
        
        for (point in gazeHistory) {
            sumX += point.x
            sumY += point.y
        }
        
        return PointF(sumX / gazeHistory.size, sumY / gazeHistory.size)
    }

    /**
     * Calculate gaze stability (lower values = more stable)
     */
    fun getGazeStability(): Float {
        if (gazeHistory.size < 2) return 1f
        
        var totalDistance = 0f
        for (i in 1 until gazeHistory.size) {
            val prev = gazeHistory[i - 1]
            val curr = gazeHistory[i]
            val distance = sqrt((curr.x - prev.x) * (curr.x - prev.x) + (curr.y - prev.y) * (curr.y - prev.y))
            totalDistance += distance
        }
        
        return totalDistance / (gazeHistory.size - 1)
    }

    /**
     * Reset tracking state
     */
    fun reset() {
        lastGazePoint = PointF(0f, 0f)
        gazeHistory.clear()
    }

    /**
     * Check if gaze is stable enough for interaction
     */
    fun isGazeStable(): Boolean {
        return getGazeStability() < 50f // Threshold for stability
    }
}
