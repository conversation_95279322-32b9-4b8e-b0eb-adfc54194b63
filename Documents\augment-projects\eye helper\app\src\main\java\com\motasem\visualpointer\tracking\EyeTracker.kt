package com.motasem.visualpointer.tracking

import android.graphics.PointF
import android.util.Log
import com.google.mlkit.vision.face.Face
import com.google.mlkit.vision.face.FaceLandmark
import com.motasem.visualpointer.model.EyeTrackingData
import com.motasem.visualpointer.utils.PreferencesManager
import kotlin.math.abs
import kotlin.math.sqrt

/**
 * Advanced face tracking implementation using nose and mouth for better accuracy
 */
class EyeTracker(private val preferencesManager: PreferencesManager) {

    companion object {
        private const val TAG = "EyeTracker"
        private const val SMOOTHING_FACTOR = 0.3f // تنعيم محسن للتتبع 3D
        private const val MIN_CONFIDENCE_THRESHOLD = 0.7f
        private const val ADAPTIVE_SMOOTHING_MIN = 0.15f // تنعيم أقل للاستجابة السريعة
        private const val ADAPTIVE_SMOOTHING_MAX = 0.6f // تنعيم أكثر للحركة البطيئة
        private const val MOVEMENT_THRESHOLD = 50f // عتبة محسنة للتتبع 3D
        private const val HEAD_ROTATION_SMOOTHING = 0.25f // تنعيم خاص لدوران الرأس
    }

    private var lastGazePoint = PointF(0f, 0f)
    private var gazeHistory = mutableListOf<PointF>()

    // متغيرات لحفظ دوران الرأس السابق للتنعيم 3D
    private var lastHeadRotationX = 0f
    private var lastHeadRotationY = 0f
    private var lastHeadRotationZ = 0f
    private val maxHistorySize = 5

    /**
     * Process face detection result and extract face tracking data using nose and mouth
     */
    fun processFrame(face: Face, screenWidth: Int, screenHeight: Int): EyeTrackingData? {
        try {
            val noseTip = face.getLandmark(FaceLandmark.NOSE_BASE)
            val mouthLeft = face.getLandmark(FaceLandmark.MOUTH_LEFT)
            val mouthRight = face.getLandmark(FaceLandmark.MOUTH_RIGHT)
            val mouthBottom = face.getLandmark(FaceLandmark.MOUTH_BOTTOM)

            // نحتاج على الأقل الأنف أو الفم للتتبع
            if (noseTip == null && mouthLeft == null && mouthRight == null) {
                Log.w(TAG, "No nose or mouth landmarks detected")
                return null
            }

            // Calculate gaze point using nose and mouth
            val gazePoint = calculateFaceTrackingPoint(face, screenWidth, screenHeight)

            // Apply smoothing
            val smoothedGaze = applySmoothingFilter(gazePoint)

            // Update history
            updateGazeHistory(smoothedGaze)

            return EyeTrackingData(
                gazeX = smoothedGaze.x,
                gazeY = smoothedGaze.y,
                leftEyeOpenProbability = face.leftEyeOpenProbability ?: 1f,
                rightEyeOpenProbability = face.rightEyeOpenProbability ?: 1f,
                faceDetected = true
            )

        } catch (e: Exception) {
            Log.e(TAG, "Error processing frame", e)
            return null
        }
    }

    /**
     * Calculate 3D face tracking using head rotation (no need to move nose physically)
     */
    private fun calculateFaceTrackingPoint(
        face: Face,
        screenWidth: Int,
        screenHeight: Int
    ): PointF {

        // الحصول على نقاط الوجه المختلفة
        val noseTip = face.getLandmark(FaceLandmark.NOSE_BASE)
        val mouthLeft = face.getLandmark(FaceLandmark.MOUTH_LEFT)
        val mouthRight = face.getLandmark(FaceLandmark.MOUTH_RIGHT)

        // حساب النقطة المرجعية (الأنف أو مركز الفم)
        val referencePoint = when {
            noseTip != null -> {
                // استخدام الأنف كنقطة مرجعية (الأفضل)
                noseTip.position
            }
            mouthLeft != null && mouthRight != null -> {
                // استخدام مركز الفم كبديل
                PointF(
                    (mouthLeft.position.x + mouthRight.position.x) / 2f,
                    (mouthLeft.position.y + mouthRight.position.y) / 2f
                )
            }
            else -> {
                // استخدام مركز الوجه كحل أخير
                val faceBox = face.boundingBox
                PointF(faceBox.centerX().toFloat(), faceBox.centerY().toFloat())
            }
        }

        // التتبع 3D الحقيقي باستخدام دوران الرأس
        return calculate3DHeadTracking(referencePoint, face, screenWidth, screenHeight)
    }

    /**
     * Calculate 3D head tracking - the main innovation!
     * User just looks around, no need to move nose physically
     */
    private fun calculate3DHeadTracking(
        referencePoint: PointF,
        face: Face,
        screenWidth: Int,
        screenHeight: Int
    ): PointF {

        // الحصول على زوايا دوران الرأس 3D
        val rawHeadRotationY = face.headEulerAngleY // يمين/يسار (-90 إلى +90)
        val rawHeadRotationX = face.headEulerAngleX // أعلى/أسفل (-90 إلى +90)
        val rawHeadRotationZ = face.headEulerAngleZ // ميل (-180 إلى +180)

        // تطبيق تنعيم على دوران الرأس للحصول على حركة أكثر سلاسة
        val headRotationY = if (lastHeadRotationY == 0f) {
            rawHeadRotationY
        } else {
            lastHeadRotationY * (1 - HEAD_ROTATION_SMOOTHING) + rawHeadRotationY * HEAD_ROTATION_SMOOTHING
        }

        val headRotationX = if (lastHeadRotationX == 0f) {
            rawHeadRotationX
        } else {
            lastHeadRotationX * (1 - HEAD_ROTATION_SMOOTHING) + rawHeadRotationX * HEAD_ROTATION_SMOOTHING
        }

        val headRotationZ = if (lastHeadRotationZ == 0f) {
            rawHeadRotationZ
        } else {
            lastHeadRotationZ * (1 - HEAD_ROTATION_SMOOTHING) + rawHeadRotationZ * HEAD_ROTATION_SMOOTHING
        }

        // حفظ القيم للمرة القادمة
        lastHeadRotationX = headRotationX
        lastHeadRotationY = headRotationY
        lastHeadRotationZ = headRotationZ

        Log.d(TAG, "3D Head Rotation (Smoothed) - X: $headRotationX, Y: $headRotationY, Z: $headRotationZ")

        // حساب موقع المؤشر بناءً على دوران الرأس فقط (بدون تحريك الأنف)
        val centerX = screenWidth / 2f
        val centerY = screenHeight / 2f

        // الحساسية المحسنة للحركة 3D
        val sensitivity = preferencesManager.getEyeTrackingSensitivity() / 100f
        val enhancedSensitivity = (sensitivity * 2.0f).coerceIn(0.5f, 3.0f) // حساسية أعلى للتتبع 3D

        // تحويل زوايا الدوران إلى إحداثيات الشاشة
        // كلما دار الرأس يميناً (+Y)، المؤشر يتحرك يميناً
        val deltaX = headRotationY * enhancedSensitivity * (screenWidth / 90f) // تطبيع للشاشة

        // كلما دار الرأس لأسفل (+X)، المؤشر يتحرك لأسفل
        val deltaY = headRotationX * enhancedSensitivity * (screenHeight / 90f) // تطبيع للشاشة

        // حساب الموقع النهائي
        var finalX = centerX + deltaX
        var finalY = centerY + deltaY

        // تطبيق تعديل بناءً على ميل الرأس (Z rotation) للدقة الإضافية
        val tiltAdjustment = headRotationZ * 0.1f // تأثير خفيف للميل
        finalX += tiltAdjustment

        // تطبيق حدود الشاشة مع هامش
        val margin = 50f
        finalX = finalX.coerceIn(margin, screenWidth.toFloat() - margin)
        finalY = finalY.coerceIn(margin, screenHeight.toFloat() - margin)

        Log.d(TAG, "3D Tracking Result: ($finalX, $finalY) from rotation ($headRotationX, $headRotationY)")

        return PointF(finalX, finalY)
    }



    /**
     * Apply curve mapping for better accuracy at screen edges
     */
    private fun applyCurveMapping(value: Float): Float {
        // تطبيق منحنى S-curve لتحسين الدقة
        val centered = (value - 0.5f) * 2f // تحويل إلى نطاق -1 إلى 1
        val curved = centered * abs(centered) // منحنى تربيعي
        return (curved / 2f) + 0.5f // إعادة إلى نطاق 0 إلى 1
    }

    /**
     * Apply calibration data to normalize gaze coordinates with enhanced accuracy
     */
    private fun applyCalibration(normalizedX: Float, normalizedY: Float): PointF {
        val calibrationData = preferencesManager.getCalibrationData()

        if (calibrationData.isNullOrEmpty()) {
            // No calibration data, apply default adjustments for better accuracy
            val adjustedX = enhanceEdgeAccuracy(normalizedX)
            val adjustedY = enhanceEdgeAccuracy(normalizedY)
            return PointF(adjustedX, adjustedY)
        }

        // Parse calibration data and apply transformation
        // This is a more sophisticated implementation
        try {
            val calibrationPoints = parseCalibrationData(calibrationData)
            if (calibrationPoints.size >= 4) {
                // استخدام interpolation بين نقاط المعايرة
                val interpolatedPoint = interpolateCalibration(normalizedX, normalizedY, calibrationPoints)
                return PointF(
                    interpolatedPoint.x.coerceIn(0f, 1f),
                    interpolatedPoint.y.coerceIn(0f, 1f)
                )
            }
        } catch (e: Exception) {
            Log.w(TAG, "Error applying calibration data", e)
        }

        // Fallback to enhanced mapping
        val adjustedX = enhanceEdgeAccuracy(normalizedX)
        val adjustedY = enhanceEdgeAccuracy(normalizedY)
        return PointF(adjustedX, adjustedY)
    }

    /**
     * Enhance accuracy at screen edges
     */
    private fun enhanceEdgeAccuracy(value: Float): Float {
        // تحسين الدقة في أطراف الشاشة
        val edgeThreshold = 0.1f
        val edgeBoost = 0.15f

        return when {
            value < edgeThreshold -> {
                // تعزيز الحركة في الطرف الأيسر/العلوي
                value * (1f + edgeBoost)
            }
            value > (1f - edgeThreshold) -> {
                // تعزيز الحركة في الطرف الأيمن/السفلي
                1f - ((1f - value) * (1f + edgeBoost))
            }
            else -> value
        }
    }

    /**
     * Parse calibration data from preferences
     */
    private fun parseCalibrationData(data: String): List<PointF> {
        // تحليل بيانات المعايرة من النص المحفوظ
        val points = mutableListOf<PointF>()
        try {
            val lines = data.split("\n")
            for (line in lines) {
                if (line.contains(",")) {
                    val coords = line.split(",")
                    if (coords.size >= 2) {
                        val x = coords[0].toFloat()
                        val y = coords[1].toFloat()
                        points.add(PointF(x, y))
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing calibration data", e)
        }
        return points
    }

    /**
     * Interpolate between calibration points
     */
    private fun interpolateCalibration(x: Float, y: Float, calibrationPoints: List<PointF>): PointF {
        // استخدام bilinear interpolation بين نقاط المعايرة
        // هذا تطبيق مبسط - في التطبيق الحقيقي نحتاج خوارزمية أكثر تعقيداً

        if (calibrationPoints.size < 4) {
            return PointF(x, y)
        }

        // العثور على أقرب نقاط المعايرة
        var closestDistance = Float.MAX_VALUE
        var closestPoint = calibrationPoints[0]

        for (point in calibrationPoints) {
            val distance = sqrt((x - point.x) * (x - point.x) + (y - point.y) * (y - point.y))
            if (distance < closestDistance) {
                closestDistance = distance
                closestPoint = point
            }
        }

        // تطبيق تعديل بسيط بناءً على أقرب نقطة
        val adjustmentFactor = 0.1f
        val adjustedX = x + (closestPoint.x - x) * adjustmentFactor
        val adjustedY = y + (closestPoint.y - y) * adjustmentFactor

        return PointF(adjustedX, adjustedY)
    }

    /**
     * Apply adaptive smoothing filter to reduce jitter while maintaining responsiveness
     */
    private fun applySmoothingFilter(newPoint: PointF): PointF {
        if (lastGazePoint.x == 0f && lastGazePoint.y == 0f) {
            lastGazePoint = newPoint
            return newPoint
        }

        // حساب المسافة بين النقطة الجديدة والسابقة
        val distance = sqrt(
            (newPoint.x - lastGazePoint.x) * (newPoint.x - lastGazePoint.x) +
            (newPoint.y - lastGazePoint.y) * (newPoint.y - lastGazePoint.y)
        )

        // تطبيق تنعيم تكيفي بناءً على سرعة الحركة
        val adaptiveSmoothingFactor = when {
            distance < MOVEMENT_THRESHOLD -> {
                // حركة بطيئة - تنعيم أكثر لتقليل الاهتزاز
                ADAPTIVE_SMOOTHING_MAX
            }
            distance > MOVEMENT_THRESHOLD * 3 -> {
                // حركة سريعة - تنعيم أقل للاستجابة السريعة
                ADAPTIVE_SMOOTHING_MIN
            }
            else -> {
                // حركة متوسطة - تنعيم متوسط
                SMOOTHING_FACTOR
            }
        }

        // تطبيق التنعيم مع الأخذ في الاعتبار تاريخ النقاط
        val smoothedX = if (gazeHistory.size >= 3) {
            // استخدام متوسط مرجح للنقاط الثلاث الأخيرة
            val weight1 = 0.5f // النقطة الحالية
            val weight2 = 0.3f // النقطة السابقة
            val weight3 = 0.2f // النقطة قبل السابقة

            newPoint.x * weight1 +
            lastGazePoint.x * weight2 +
            gazeHistory[gazeHistory.size - 2].x * weight3
        } else {
            lastGazePoint.x * (1 - adaptiveSmoothingFactor) + newPoint.x * adaptiveSmoothingFactor
        }

        val smoothedY = if (gazeHistory.size >= 3) {
            val weight1 = 0.5f
            val weight2 = 0.3f
            val weight3 = 0.2f

            newPoint.y * weight1 +
            lastGazePoint.y * weight2 +
            gazeHistory[gazeHistory.size - 2].y * weight3
        } else {
            lastGazePoint.y * (1 - adaptiveSmoothingFactor) + newPoint.y * adaptiveSmoothingFactor
        }

        val smoothedPoint = PointF(smoothedX, smoothedY)
        lastGazePoint = smoothedPoint

        Log.d(TAG, "Distance: $distance, Smoothing: $adaptiveSmoothingFactor")

        return smoothedPoint
    }

    /**
     * Update gaze history for advanced filtering
     */
    private fun updateGazeHistory(point: PointF) {
        gazeHistory.add(point)
        if (gazeHistory.size > maxHistorySize) {
            gazeHistory.removeAt(0)
        }
    }

    /**
     * Get average gaze point from history
     */
    fun getAverageGazePoint(): PointF? {
        if (gazeHistory.isEmpty()) return null
        
        var sumX = 0f
        var sumY = 0f
        
        for (point in gazeHistory) {
            sumX += point.x
            sumY += point.y
        }
        
        return PointF(sumX / gazeHistory.size, sumY / gazeHistory.size)
    }

    /**
     * Calculate gaze stability (lower values = more stable)
     */
    fun getGazeStability(): Float {
        if (gazeHistory.size < 2) return 1f
        
        var totalDistance = 0f
        for (i in 1 until gazeHistory.size) {
            val prev = gazeHistory[i - 1]
            val curr = gazeHistory[i]
            val distance = sqrt((curr.x - prev.x) * (curr.x - prev.x) + (curr.y - prev.y) * (curr.y - prev.y))
            totalDistance += distance
        }
        
        return totalDistance / (gazeHistory.size - 1)
    }

    /**
     * Reset tracking state including 3D head rotation history
     */
    fun reset() {
        lastGazePoint = PointF(0f, 0f)
        gazeHistory.clear()

        // إعادة تعيين دوران الرأس
        lastHeadRotationX = 0f
        lastHeadRotationY = 0f
        lastHeadRotationZ = 0f

        Log.d(TAG, "3D Head tracking reset")
    }

    /**
     * Check if gaze is stable enough for interaction
     */
    fun isGazeStable(): Boolean {
        return getGazeStability() < 50f // Threshold for stability
    }
}
