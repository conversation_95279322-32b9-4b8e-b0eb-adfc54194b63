<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.motasem.visualpointer" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="187" endOffset="51"/></Target><Target id="@+id/cameraContainer" view="FrameLayout"><Expressions/><location startLine="10" startOffset="4" endLine="71" endOffset="17"/></Target><Target id="@+id/previewView" view="androidx.camera.view.PreviewView"><Expressions/><location startLine="22" startOffset="8" endLine="27" endOffset="40"/></Target><Target id="@+id/graphicOverlay" view="com.motasem.visualpointer.ui.GraphicOverlay"><Expressions/><location startLine="30" startOffset="8" endLine="34" endOffset="41"/></Target><Target id="@+id/instructionsOverlay" view="LinearLayout"><Expressions/><location startLine="37" startOffset="8" endLine="69" endOffset="22"/></Target><Target id="@+id/titleContainer" view="LinearLayout"><Expressions/><location startLine="74" startOffset="4" endLine="101" endOffset="18"/></Target><Target id="@+id/titleText" view="TextView"><Expressions/><location startLine="85" startOffset="8" endLine="91" endOffset="44"/></Target><Target id="@+id/subtitleText" view="TextView"><Expressions/><location startLine="93" startOffset="8" endLine="99" endOffset="44"/></Target><Target id="@+id/statusContainer" view="LinearLayout"><Expressions/><location startLine="104" startOffset="4" endLine="123" endOffset="18"/></Target><Target id="@+id/statusText" view="TextView"><Expressions/><location startLine="115" startOffset="8" endLine="121" endOffset="44"/></Target><Target id="@+id/buttonContainer" view="LinearLayout"><Expressions/><location startLine="126" startOffset="4" endLine="185" endOffset="18"/></Target><Target id="@+id/startTrackingButton" view="Button"><Expressions/><location startLine="137" startOffset="8" endLine="141" endOffset="51"/></Target><Target id="@+id/settingsButton" view="Button"><Expressions/><location startLine="148" startOffset="12" endLine="153" endOffset="49"/></Target><Target id="@+id/calibrationButton" view="Button"><Expressions/><location startLine="155" startOffset="12" endLine="160" endOffset="52"/></Target><Target id="@+id/helpButton" view="Button"><Expressions/><location startLine="169" startOffset="12" endLine="174" endOffset="45"/></Target><Target id="@+id/aboutButton" view="Button"><Expressions/><location startLine="176" startOffset="12" endLine="181" endOffset="45"/></Target></Targets></Layout>