# دليل التثبيت والتشغيل - المؤشر البصري

## متطلبات النظام

### متطلبات التطوير
- **Android Studio**: Arctic Fox (2020.3.1) أو أحدث
- **JDK**: 8 أو أحدث
- **Android SDK**: API 26 (Android 8.0) أو أحدث
- **Gradle**: 8.0 أو أحدث

### متطلبات الجهاز
- **نظام التشغيل**: Android 8.0 (API 26) أو أحدث
- **الكاميرا**: كاميرا أمامية عالية الجودة
- **الذاكرة**: 3 جيجابايت RAM أو أكثر
- **التخزين**: 100 ميجابايت مساحة فارغة

## خطوات التثبيت

### 1. إعداد بيئة التطوير

#### تثبيت Android Studio
1. قم بتحميل Android Studio من [الموقع الرسمي](https://developer.android.com/studio)
2. اتبع تعليمات التثبيت لنظام التشغيل الخاص بك
3. تأكد من تثبيت Android SDK وأدوات البناء

#### إعداد المتغيرات البيئية
```bash
# Windows
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
set PATH=%PATH%;%ANDROID_HOME%\tools;%ANDROID_HOME%\platform-tools

# macOS/Linux
export ANDROID_HOME=$HOME/Android/Sdk
export PATH=$PATH:$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools
```

### 2. استنساخ المشروع

```bash
git clone https://github.com/motasem-salem/visual-pointer.git
cd visual-pointer
```

### 3. فتح المشروع في Android Studio

1. افتح Android Studio
2. اختر "Open an existing Android Studio project"
3. انتقل إلى مجلد المشروع واختره
4. انتظر حتى يكتمل تحميل المشروع وفهرسة الملفات

### 4. تكوين المشروع

#### تحديث local.properties
```properties
sdk.dir=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk
```

#### التحقق من إعدادات Gradle
تأكد من أن ملف `app/build.gradle` يحتوي على الإعدادات الصحيحة:

```gradle
android {
    compileSdk 34
    
    defaultConfig {
        minSdk 26
        targetSdk 34
    }
}
```

### 5. تثبيت التبعيات

سيقوم Android Studio بتحميل التبعيات تلقائياً. إذا لم يحدث ذلك:

1. اضغط على "Sync Project with Gradle Files"
2. أو استخدم الأمر: `./gradlew build`

### 6. بناء المشروع

#### من Android Studio
1. اختر "Build" من القائمة العلوية
2. اضغط على "Make Project" أو استخدم Ctrl+F9

#### من سطر الأوامر
```bash
# Windows
gradlew.bat assembleDebug

# macOS/Linux
./gradlew assembleDebug
```

### 7. تشغيل التطبيق

#### على جهاز حقيقي (مُوصى به)
1. فعّل "Developer Options" على جهازك
2. فعّل "USB Debugging"
3. وصّل الجهاز بالكمبيوتر
4. اضغط على "Run" في Android Studio

#### على محاكي
1. أنشئ AVD جديد من AVD Manager
2. اختر جهاز بكاميرا أمامية
3. شغّل المحاكي واضغط على "Run"

## إعداد الصلاحيات

بعد تثبيت التطبيق، ستحتاج إلى منح الصلاحيات التالية:

### 1. صلاحية الكاميرا
- سيطلب التطبيق هذه الصلاحية تلقائياً
- أو اذهب إلى: الإعدادات > التطبيقات > المؤشر البصري > الصلاحيات

### 2. صلاحية العرض فوق التطبيقات
- الإعدادات > التطبيقات > إعدادات خاصة > العرض فوق التطبيقات الأخرى
- ابحث عن "المؤشر البصري" وفعّل الصلاحية

### 3. خدمة إمكانية الوصول
- الإعدادات > إمكانية الوصول
- ابحث عن "المؤشر البصري" وفعّل الخدمة

## استكشاف الأخطاء وإصلاحها

### مشاكل البناء الشائعة

#### خطأ في SDK
```
Error: SDK location not found
```
**الحل**: تأكد من إعداد `ANDROID_HOME` وملف `local.properties`

#### خطأ في Gradle
```
Could not resolve all dependencies
```
**الحل**: 
1. تحقق من اتصال الإنترنت
2. نظّف المشروع: `./gradlew clean`
3. أعد البناء: `./gradlew build`

#### خطأ في ML Kit
```
ML Kit dependencies not found
```
**الحل**: تأكد من إضافة Google Maven repository في `build.gradle`

### مشاكل وقت التشغيل

#### الكاميرا لا تعمل
- تحقق من صلاحية الكاميرا
- تأكد من عدم استخدام تطبيق آخر للكاميرا
- أعد تشغيل التطبيق

#### المؤشر لا يظهر
- تحقق من صلاحية العرض فوق التطبيقات
- تأكد من تشغيل الخدمة في الخلفية

#### الغمزات لا تُكتشف
- تحسين الإضاءة
- اضبط حساسية اكتشاف الغمز في الإعدادات
- تأكد من وضوح العينين في الكاميرا

## اختبار التطبيق

### اختبار أساسي
1. شغّل التطبيق
2. امنح جميع الصلاحيات المطلوبة
3. اضغط على "بدء التتبع"
4. تحقق من ظهور المؤشر الأحمر
5. جرّب الغمزات المختلفة

### اختبار متقدم
1. اذهب إلى شاشة المعايرة
2. أكمل عملية المعايرة
3. اضبط الإعدادات حسب تفضيلاتك
4. اختبر التطبيق في تطبيقات مختلفة

## نصائح للأداء الأمثل

### للمطورين
- استخدم جهاز حقيقي للاختبار (ليس محاكي)
- فعّل "Instant Run" لتطوير أسرع
- استخدم Profiler لمراقبة الأداء

### للمستخدمين
- تأكد من إضاءة جيدة
- احتفظ بمسافة 30-50 سم من الشاشة
- نظّف عدسة الكاميرا الأمامية
- أغلق التطبيقات غير المستخدمة لتوفير الذاكرة

## إنشاء APK للتوزيع

### Debug APK
```bash
./gradlew assembleDebug
```
الملف سيكون في: `app/build/outputs/apk/debug/`

### Release APK
```bash
./gradlew assembleRelease
```
الملف سيكون في: `app/build/outputs/apk/release/`

### توقيع APK
لتوزيع التطبيق، ستحتاج إلى توقيعه:

1. أنشئ keystore:
```bash
keytool -genkey -v -keystore visual-pointer.keystore -alias visual-pointer -keyalg RSA -keysize 2048 -validity 10000
```

2. أضف إعدادات التوقيع في `app/build.gradle`:
```gradle
android {
    signingConfigs {
        release {
            storeFile file('visual-pointer.keystore')
            storePassword 'your-password'
            keyAlias 'visual-pointer'
            keyPassword 'your-password'
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
        }
    }
}
```

## الدعم والمساعدة

إذا واجهت أي مشاكل:

1. تحقق من [قسم استكشاف الأخطاء](#استكشاف-الأخطاء-وإصلاحها)
2. راجع [الوثائق الرسمية](README.md)
3. تواصل مع المطور: واتساب 01062606098

## تحديث التطبيق

لتحديث التطبيق لأحدث إصدار:

```bash
git pull origin main
./gradlew clean
./gradlew build
```

---

**ملاحظة**: هذا التطبيق مصمم لمساعدة الأشخاص ذوي الإعاقات الحركية. يرجى اختباره بعناية قبل الاستخدام الفعلي.
