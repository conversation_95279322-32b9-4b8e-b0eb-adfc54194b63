1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.motasem.visualpointer"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <!-- Camera permissions -->
12    <uses-feature android:name="android.hardware.camera.any" />
12-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:6:5-64
12-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:6:19-61
13
14    <uses-permission android:name="android.permission.CAMERA" />
14-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:7:5-65
14-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:7:22-62
15
16    <!-- Overlay permission for floating cursor -->
17    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
17-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:10:5-78
17-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:10:22-75
18
19    <!-- Accessibility service permission -->
20    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
20-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:13:5-85
20-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:13:22-82
21
22    <!-- Foreground service permissions (Required for Android 9+) -->
23    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
23-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:16:5-77
23-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:16:22-74
24    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CAMERA" />
24-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:17:5-84
24-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:17:22-81
25    <uses-permission android:name="android.permission.WAKE_LOCK" />
25-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:18:5-68
25-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:18:22-65
26
27    <!-- Battery optimization permissions -->
28    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
28-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:21:5-95
28-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:21:22-92
29    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
29-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:22:5-75
29-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:22:22-72
30    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
30-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:23:5-81
30-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:23:22-78
31
32    <permission
32-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
33        android:name="com.motasem.visualpointer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
33-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
34        android:protectionLevel="signature" />
34-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
35
36    <uses-permission android:name="com.motasem.visualpointer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
36-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
36-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
37    <!-- <uses-sdk android:minSdkVersion="14"/> -->
38    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
38-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
38-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:22-76
39    <uses-permission android:name="android.permission.INTERNET" />
39-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
39-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:22-64
40
41    <application
41-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:25:5-88:19
42        android:allowBackup="true"
42-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:26:9-35
43        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
43-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
44        android:dataExtractionRules="@xml/data_extraction_rules"
44-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:27:9-65
45        android:debuggable="true"
46        android:extractNativeLibs="false"
47        android:fullBackupContent="@xml/backup_rules"
47-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:28:9-54
48        android:icon="@mipmap/ic_launcher"
48-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:29:9-43
49        android:label="@string/app_name"
49-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:30:9-41
50        android:roundIcon="@mipmap/ic_launcher_round"
50-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:31:9-54
51        android:supportsRtl="true"
51-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:32:9-35
52        android:theme="@style/Theme.VisualPointer" >
52-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:33:9-51
53
54        <!-- ML Kit automatic model download -->
55        <meta-data
55-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:37:9-39:36
56            android:name="com.google.mlkit.vision.DEPENDENCIES"
56-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:38:13-64
57            android:value="face" />
57-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:39:13-33
58
59        <!-- Main Activity -->
60        <activity
60-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:42:9-50:20
61            android:name="com.motasem.visualpointer.MainActivity"
61-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:43:13-41
62            android:exported="true"
62-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:44:13-36
63            android:screenOrientation="portrait" >
63-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:45:13-49
64            <intent-filter>
64-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:46:13-49:29
65                <action android:name="android.intent.action.MAIN" />
65-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:47:17-69
65-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:47:25-66
66
67                <category android:name="android.intent.category.LAUNCHER" />
67-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:48:17-77
67-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:48:27-74
68            </intent-filter>
69        </activity>
70
71        <!-- Settings Activity -->
72        <activity
72-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:53:9-56:52
73            android:name="com.motasem.visualpointer.ui.SettingsActivity"
73-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:54:13-48
74            android:exported="false"
74-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:55:13-37
75            android:screenOrientation="portrait" />
75-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:56:13-49
76
77        <!-- Calibration Activity -->
78        <activity
78-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:59:9-62:52
79            android:name="com.motasem.visualpointer.ui.CalibrationActivity"
79-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:60:13-51
80            android:exported="false"
80-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:61:13-37
81            android:screenOrientation="portrait" />
81-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:62:13-49
82
83        <!-- Accessibility Service -->
84        <service
84-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:65:9-75:19
85            android:name="com.motasem.visualpointer.service.VisualPointerAccessibilityService"
85-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:66:13-70
86            android:exported="false"
86-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:67:13-37
87            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
87-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:68:13-79
88            <intent-filter>
88-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:69:13-71:29
89                <action android:name="android.accessibilityservice.AccessibilityService" />
89-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:70:17-92
89-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:70:25-89
90            </intent-filter>
91
92            <meta-data
92-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:72:13-74:72
93                android:name="android.accessibilityservice"
93-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:73:17-60
94                android:resource="@xml/accessibility_service_config" />
94-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:74:17-69
95        </service>
96
97        <!-- Eye Tracking Service -->
98        <service
98-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:78:9-86:19
99            android:name="com.motasem.visualpointer.service.EyeTrackingService"
99-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:79:13-55
100            android:enabled="true"
100-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:81:13-35
101            android:exported="false"
101-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:80:13-37
102            android:foregroundServiceType="camera|specialUse" >
102-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:82:13-62
103            <property
103-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:83:13-85:48
104                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
104-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:84:17-76
105                android:value="eye_tracking" />
105-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:85:17-45
106        </service>
107        <service
107-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
108            android:name="androidx.camera.core.impl.MetadataHolderService"
108-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
109            android:enabled="false"
109-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
110            android:exported="false" >
110-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
111            <meta-data
111-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
112                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
112-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
113                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
113-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
114        </service>
115        <service
115-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc8266ca8ba43cd1000e107fd6537138\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:9:9-15:19
116            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
116-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc8266ca8ba43cd1000e107fd6537138\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:10:13-91
117            android:directBootAware="true"
117-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:17:13-43
118            android:exported="false" >
118-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc8266ca8ba43cd1000e107fd6537138\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:11:13-37
119            <meta-data
119-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc8266ca8ba43cd1000e107fd6537138\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:12:13-14:85
120                android:name="com.google.firebase.components:com.google.mlkit.vision.face.internal.FaceRegistrar"
120-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc8266ca8ba43cd1000e107fd6537138\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:13:17-114
121                android:value="com.google.firebase.components.ComponentRegistrar" />
121-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc8266ca8ba43cd1000e107fd6537138\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:14:17-82
122            <meta-data
122-->[com.google.mlkit:vision-common:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ec3a4b3cfcbc8b6ee37d1fd668aaa9\transformed\vision-common-17.2.0\AndroidManifest.xml:12:13-14:85
123                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
123-->[com.google.mlkit:vision-common:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ec3a4b3cfcbc8b6ee37d1fd668aaa9\transformed\vision-common-17.2.0\AndroidManifest.xml:13:17-124
124                android:value="com.google.firebase.components.ComponentRegistrar" />
124-->[com.google.mlkit:vision-common:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ec3a4b3cfcbc8b6ee37d1fd668aaa9\transformed\vision-common-17.2.0\AndroidManifest.xml:14:17-82
125            <meta-data
125-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:20:13-22:85
126                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
126-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:21:17-120
127                android:value="com.google.firebase.components.ComponentRegistrar" />
127-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:22:17-82
128        </service>
129
130        <provider
130-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:9:9-13:38
131            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
131-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:10:13-78
132            android:authorities="com.motasem.visualpointer.mlkitinitprovider"
132-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:11:13-69
133            android:exported="false"
133-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:12:13-37
134            android:initOrder="99" />
134-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:13:13-35
135
136        <activity
136-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f08e0d719849b14ed4ab8905bd80cf7\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
137            android:name="com.google.android.gms.common.api.GoogleApiActivity"
137-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f08e0d719849b14ed4ab8905bd80cf7\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
138            android:exported="false"
138-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f08e0d719849b14ed4ab8905bd80cf7\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
139            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
139-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f08e0d719849b14ed4ab8905bd80cf7\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
140
141        <meta-data
141-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\949294b35df779989b974dc81fe3e123\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
142            android:name="com.google.android.gms.version"
142-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\949294b35df779989b974dc81fe3e123\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
143            android:value="@integer/google_play_services_version" />
143-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\949294b35df779989b974dc81fe3e123\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
144
145        <provider
145-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
146            android:name="androidx.startup.InitializationProvider"
146-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
147            android:authorities="com.motasem.visualpointer.androidx-startup"
147-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
148            android:exported="false" >
148-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
149            <meta-data
149-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
150                android:name="androidx.emoji2.text.EmojiCompatInitializer"
150-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
151                android:value="androidx.startup" />
151-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
152            <meta-data
152-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d68bc86f32b54e9d000deb9c917a49e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
153                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
153-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d68bc86f32b54e9d000deb9c917a49e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
154                android:value="androidx.startup" />
154-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d68bc86f32b54e9d000deb9c917a49e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
155            <meta-data
155-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
156                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
156-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
157                android:value="androidx.startup" />
157-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
158        </provider>
159
160        <service
160-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
161            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
161-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
162            android:exported="false" >
162-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
163            <meta-data
163-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
164                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
164-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
165                android:value="cct" />
165-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
166        </service>
167        <service
167-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
168            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
168-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
169            android:exported="false"
169-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
170            android:permission="android.permission.BIND_JOB_SERVICE" >
170-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
171        </service>
172
173        <receiver
173-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
174            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
174-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
175            android:exported="false" />
175-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
176        <receiver
176-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
177            android:name="androidx.profileinstaller.ProfileInstallReceiver"
177-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
178            android:directBootAware="false"
178-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
179            android:enabled="true"
179-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
180            android:exported="true"
180-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
181            android:permission="android.permission.DUMP" >
181-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
182            <intent-filter>
182-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
183                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
183-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
183-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
184            </intent-filter>
185            <intent-filter>
185-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
186                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
186-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
186-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
187            </intent-filter>
188            <intent-filter>
188-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
189                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
189-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
189-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
190            </intent-filter>
191            <intent-filter>
191-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
192                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
192-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
192-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
193            </intent-filter>
194        </receiver>
195    </application>
196
197</manifest>
