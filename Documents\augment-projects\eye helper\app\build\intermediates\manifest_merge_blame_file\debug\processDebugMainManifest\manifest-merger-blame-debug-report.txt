1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.motasem.visualpointer"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <!-- Camera permissions -->
12    <uses-feature android:name="android.hardware.camera.any" />
12-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:6:5-64
12-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:6:19-61
13
14    <uses-permission android:name="android.permission.CAMERA" />
14-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:7:5-65
14-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:7:22-62
15
16    <!-- Overlay permission for floating cursor -->
17    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
17-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:10:5-78
17-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:10:22-75
18
19    <!-- Accessibility service permission -->
20    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
20-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:13:5-85
20-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:13:22-82
21
22    <!-- Foreground service permissions (Required for Android 9+) -->
23    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
23-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:16:5-77
23-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:16:22-74
24    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CAMERA" />
24-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:17:5-84
24-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:17:22-81
25    <uses-permission android:name="android.permission.WAKE_LOCK" />
25-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:18:5-68
25-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:18:22-65
26
27    <!-- Battery optimization permissions -->
28    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
28-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:21:5-95
28-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:21:22-92
29    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
29-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:22:5-75
29-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:22:22-72
30    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
30-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:23:5-81
30-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:23:22-78
31
32    <!-- Additional permissions for background execution -->
33    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
33-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:26:5-79
33-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:26:22-76
34    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
34-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:27:5-74
34-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:27:22-71
35    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
35-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:28:5-77
35-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:28:22-74
36    <uses-permission android:name="android.permission.VIBRATE" />
36-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:29:5-66
36-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:29:22-63
37
38    <permission
38-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
39        android:name="com.motasem.visualpointer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
39-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
40        android:protectionLevel="signature" />
40-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
41
42    <uses-permission android:name="com.motasem.visualpointer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
42-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
42-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
43    <!-- <uses-sdk android:minSdkVersion="14"/> -->
44    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
44-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
44-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:22-76
45    <uses-permission android:name="android.permission.INTERNET" />
45-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
45-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:22-64
46
47    <application
47-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:31:5-115:19
48        android:allowBackup="true"
48-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:32:9-35
49        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
49-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb17ecbf9872120b924d2bbfd7cfe0e6\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
50        android:dataExtractionRules="@xml/data_extraction_rules"
50-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:33:9-65
51        android:debuggable="true"
52        android:extractNativeLibs="false"
53        android:fullBackupContent="@xml/backup_rules"
53-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:34:9-54
54        android:icon="@mipmap/ic_launcher"
54-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:35:9-43
55        android:label="@string/app_name"
55-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:36:9-41
56        android:roundIcon="@mipmap/ic_launcher_round"
56-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:37:9-54
57        android:supportsRtl="true"
57-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:38:9-35
58        android:theme="@style/Theme.VisualPointer" >
58-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:39:9-51
59
60        <!-- ML Kit automatic model download -->
61        <meta-data
61-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:43:9-45:36
62            android:name="com.google.mlkit.vision.DEPENDENCIES"
62-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:44:13-64
63            android:value="face" />
63-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:45:13-33
64
65        <!-- Main Activity -->
66        <activity
66-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:48:9-56:20
67            android:name="com.motasem.visualpointer.MainActivity"
67-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:49:13-41
68            android:exported="true"
68-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:50:13-36
69            android:screenOrientation="portrait" >
69-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:51:13-49
70            <intent-filter>
70-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:52:13-55:29
71                <action android:name="android.intent.action.MAIN" />
71-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:53:17-69
71-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:53:25-66
72
73                <category android:name="android.intent.category.LAUNCHER" />
73-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:54:17-77
73-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:54:27-74
74            </intent-filter>
75        </activity>
76
77        <!-- Settings Activity -->
78        <activity
78-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:59:9-62:52
79            android:name="com.motasem.visualpointer.ui.SettingsActivity"
79-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:60:13-48
80            android:exported="false"
80-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:61:13-37
81            android:screenOrientation="portrait" />
81-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:62:13-49
82
83        <!-- Calibration Activity -->
84        <activity
84-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:65:9-68:52
85            android:name="com.motasem.visualpointer.ui.CalibrationActivity"
85-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:66:13-51
86            android:exported="false"
86-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:67:13-37
87            android:screenOrientation="portrait" />
87-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:68:13-49
88
89        <!-- About Activity -->
90        <activity
90-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:71:9-75:58
91            android:name="com.motasem.visualpointer.ui.AboutActivity"
91-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:72:13-45
92            android:exported="false"
92-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:73:13-37
93            android:screenOrientation="portrait"
93-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:74:13-49
94            android:theme="@style/Theme.VisualPointer" />
94-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:75:13-55
95
96        <!-- Accessibility Service -->
97        <service
97-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:78:9-88:19
98            android:name="com.motasem.visualpointer.service.VisualPointerAccessibilityService"
98-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:79:13-70
99            android:exported="false"
99-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:80:13-37
100            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
100-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:81:13-79
101            <intent-filter>
101-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:82:13-84:29
102                <action android:name="android.accessibilityservice.AccessibilityService" />
102-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:83:17-92
102-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:83:25-89
103            </intent-filter>
104
105            <meta-data
105-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:85:13-87:72
106                android:name="android.accessibilityservice"
106-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:86:17-60
107                android:resource="@xml/accessibility_service_config" />
107-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:87:17-69
108        </service>
109
110        <!-- Eye Tracking Service -->
111        <service
111-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:91:9-99:19
112            android:name="com.motasem.visualpointer.service.EyeTrackingService"
112-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:92:13-55
113            android:enabled="true"
113-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:94:13-35
114            android:exported="false"
114-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:93:13-37
115            android:foregroundServiceType="camera|specialUse" >
115-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:95:13-62
116            <property
116-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:96:13-98:48
117                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
117-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:97:17-76
118                android:value="eye_tracking" />
118-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:98:17-45
119        </service>
120
121        <!-- Service Restart Receiver -->
122        <receiver
122-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:102:9-113:20
123            android:name="com.motasem.visualpointer.service.ServiceRestartReceiver"
123-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:103:13-59
124            android:enabled="true"
124-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:105:13-35
125            android:exported="false" >
125-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:104:13-37
126            <intent-filter android:priority="1000" >
126-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:106:13-112:29
126-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:106:28-51
127                <action android:name="android.intent.action.BOOT_COMPLETED" />
127-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:107:17-79
127-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:107:25-76
128                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
128-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:108:17-84
128-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:108:25-81
129                <action android:name="android.intent.action.PACKAGE_REPLACED" />
129-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:109:17-81
129-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:109:25-78
130                <action android:name="com.motasem.visualpointer.RESTART_SERVICE" />
130-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:110:17-84
130-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:110:25-81
131
132                <data android:scheme="package" />
132-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:111:17-50
132-->C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\AndroidManifest.xml:111:23-47
133            </intent-filter>
134        </receiver>
135
136        <service
136-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
137            android:name="androidx.camera.core.impl.MetadataHolderService"
137-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
138            android:enabled="false"
138-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
139            android:exported="false" >
139-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
140            <meta-data
140-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
141                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
141-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
142                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
142-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e910602de0e5f6bac0376c1bf4b99291\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
143        </service>
144        <service
144-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc8266ca8ba43cd1000e107fd6537138\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:9:9-15:19
145            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
145-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc8266ca8ba43cd1000e107fd6537138\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:10:13-91
146            android:directBootAware="true"
146-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:17:13-43
147            android:exported="false" >
147-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc8266ca8ba43cd1000e107fd6537138\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:11:13-37
148            <meta-data
148-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc8266ca8ba43cd1000e107fd6537138\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:12:13-14:85
149                android:name="com.google.firebase.components:com.google.mlkit.vision.face.internal.FaceRegistrar"
149-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc8266ca8ba43cd1000e107fd6537138\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:13:17-114
150                android:value="com.google.firebase.components.ComponentRegistrar" />
150-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc8266ca8ba43cd1000e107fd6537138\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:14:17-82
151            <meta-data
151-->[com.google.mlkit:vision-common:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ec3a4b3cfcbc8b6ee37d1fd668aaa9\transformed\vision-common-17.2.0\AndroidManifest.xml:12:13-14:85
152                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
152-->[com.google.mlkit:vision-common:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ec3a4b3cfcbc8b6ee37d1fd668aaa9\transformed\vision-common-17.2.0\AndroidManifest.xml:13:17-124
153                android:value="com.google.firebase.components.ComponentRegistrar" />
153-->[com.google.mlkit:vision-common:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ec3a4b3cfcbc8b6ee37d1fd668aaa9\transformed\vision-common-17.2.0\AndroidManifest.xml:14:17-82
154            <meta-data
154-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:20:13-22:85
155                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
155-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:21:17-120
156                android:value="com.google.firebase.components.ComponentRegistrar" />
156-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:22:17-82
157        </service>
158
159        <provider
159-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:9:9-13:38
160            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
160-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:10:13-78
161            android:authorities="com.motasem.visualpointer.mlkitinitprovider"
161-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:11:13-69
162            android:exported="false"
162-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:12:13-37
163            android:initOrder="99" />
163-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf4108af7fac9d7155b806db49f34691\transformed\common-18.5.0\AndroidManifest.xml:13:13-35
164
165        <activity
165-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f08e0d719849b14ed4ab8905bd80cf7\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
166            android:name="com.google.android.gms.common.api.GoogleApiActivity"
166-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f08e0d719849b14ed4ab8905bd80cf7\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
167            android:exported="false"
167-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f08e0d719849b14ed4ab8905bd80cf7\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
168            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
168-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f08e0d719849b14ed4ab8905bd80cf7\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
169
170        <meta-data
170-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\949294b35df779989b974dc81fe3e123\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
171            android:name="com.google.android.gms.version"
171-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\949294b35df779989b974dc81fe3e123\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
172            android:value="@integer/google_play_services_version" />
172-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\949294b35df779989b974dc81fe3e123\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
173
174        <provider
174-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
175            android:name="androidx.startup.InitializationProvider"
175-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
176            android:authorities="com.motasem.visualpointer.androidx-startup"
176-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
177            android:exported="false" >
177-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
178            <meta-data
178-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
179                android:name="androidx.emoji2.text.EmojiCompatInitializer"
179-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
180                android:value="androidx.startup" />
180-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ae73953844573314b2ad719346ac03d\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
181            <meta-data
181-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d68bc86f32b54e9d000deb9c917a49e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
182                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
182-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d68bc86f32b54e9d000deb9c917a49e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
183                android:value="androidx.startup" />
183-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d68bc86f32b54e9d000deb9c917a49e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
184            <meta-data
184-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
185                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
185-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
186                android:value="androidx.startup" />
186-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
187        </provider>
188
189        <service
189-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
190            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
190-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
191            android:exported="false" >
191-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
192            <meta-data
192-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
193                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
193-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
194                android:value="cct" />
194-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e35ca0cabe050cb58cbbf6fc7e75fb16\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
195        </service>
196        <service
196-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
197            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
197-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
198            android:exported="false"
198-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
199            android:permission="android.permission.BIND_JOB_SERVICE" >
199-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
200        </service>
201
202        <receiver
202-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
203            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
203-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
204            android:exported="false" />
204-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d2f0075c6ddc0610ba079b0cde8431\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
205        <receiver
205-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
206            android:name="androidx.profileinstaller.ProfileInstallReceiver"
206-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
207            android:directBootAware="false"
207-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
208            android:enabled="true"
208-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
209            android:exported="true"
209-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
210            android:permission="android.permission.DUMP" >
210-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
211            <intent-filter>
211-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
212                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
212-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
212-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
213            </intent-filter>
214            <intent-filter>
214-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
215                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
215-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
215-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
216            </intent-filter>
217            <intent-filter>
217-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
218                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
218-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
218-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
219            </intent-filter>
220            <intent-filter>
220-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
221                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
221-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
221-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5db1fda73c4a7e8485c2f1576100dde\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
222            </intent-filter>
223        </receiver>
224    </application>
225
226</manifest>
