package com.motasem.visualpointer.camera

import android.graphics.Rect
import android.util.Log
import androidx.camera.core.ImageProxy
import com.google.android.gms.tasks.Task
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.face.Face
import com.google.mlkit.vision.face.FaceDetection
import com.google.mlkit.vision.face.FaceDetectorOptions
import com.motasem.visualpointer.model.TrackingStatus
import com.motasem.visualpointer.service.EyeTrackingService
import com.motasem.visualpointer.ui.FaceGraphic
import com.motasem.visualpointer.ui.GraphicOverlay
import java.io.IOException

/**
 * Face detection analyzer using ML Kit
 */
class FaceDetectionAnalyzer(
    private val graphicOverlayView: GraphicOverlay,
    private val listener: (TrackingStatus, Face?) -> Unit
) : Analyzer<List<Face>>() {

    companion object {
        private const val TAG = "FaceDetectionAnalyzer"
    }

    private val realTimeOpts = FaceDetectorOptions.Builder()
        .setPerformanceMode(FaceDetectorOptions.PERFORMANCE_MODE_FAST)
        .setContourMode(FaceDetectorOptions.CONTOUR_MODE_NONE)
        .setClassificationMode(FaceDetectorOptions.CLASSIFICATION_MODE_ALL)
        .setMinFaceSize(0.15f)
        .enableTracking()
        .build()

    private val detector = FaceDetection.getClient(realTimeOpts)

    override val graphicOverlay: GraphicOverlay
        get() = graphicOverlayView

    override fun detectInImage(image: InputImage): Task<List<Face>> {
        return detector.process(image)
    }

    override fun stop() {
        try {
            detector.close()
        } catch (e: IOException) {
            Log.e(TAG, "Exception thrown while trying to close Face Detector: $e")
        }
    }

    override fun onSuccess(
        results: List<Face>,
        graphicOverlay: GraphicOverlay,
        rect: Rect,
        imageProxy: ImageProxy
    ) {
        graphicOverlay.clear()
        
        when {
            results.isEmpty() -> {
                listener(TrackingStatus.NO_FACE, null)
                Log.d(TAG, "No face detected")
            }
            results.size > 1 -> {
                listener(TrackingStatus.MULTIPLE_FACES, null)
                Log.d(TAG, "Multiple faces detected: ${results.size}")
            }
            else -> {
                val face = results[0]
                val faceGraphic = FaceGraphic(graphicOverlay, face, rect, listener)
                graphicOverlay.add(faceGraphic)
                graphicOverlay.postInvalidate()
                
                // Analyze eye states
                analyzeFaceAndEyes(face)
            }
        }
    }

    override fun onFailure(exception: Exception) {
        Log.e(TAG, "Face Detector failed: $exception")
        listener(TrackingStatus.ERROR, null)
    }

    private fun analyzeFaceAndEyes(face: Face) {
        val leftEyeProbability = face.leftEyeOpenProbability ?: 1.0f
        val rightEyeProbability = face.rightEyeOpenProbability ?: 1.0f

        val eyeClosedThreshold = 0.6f

        // Send data to eye tracking service if available
        val eyeTrackingService = EyeTrackingService.getInstance()
        eyeTrackingService?.let { service ->
            // Calculate gaze position (simplified)
            val faceBox = face.boundingBox
            val gazeX = faceBox.centerX().toFloat()
            val gazeY = faceBox.centerY().toFloat()

            service.processEyeTrackingData(leftEyeProbability, rightEyeProbability, gazeX, gazeY)
        }

        when {
            leftEyeProbability <= eyeClosedThreshold && rightEyeProbability <= eyeClosedThreshold -> {
                listener(TrackingStatus.BOTH_EYES_CLOSED, face)
            }
            leftEyeProbability <= eyeClosedThreshold -> {
                listener(TrackingStatus.LEFT_EYE_CLOSED, face)
            }
            rightEyeProbability <= eyeClosedThreshold -> {
                listener(TrackingStatus.RIGHT_EYE_CLOSED, face)
            }
            else -> {
                listener(TrackingStatus.FACE_DETECTED, face)
            }
        }

        Log.d(TAG, "Left eye: $leftEyeProbability, Right eye: $rightEyeProbability")
    }
}
