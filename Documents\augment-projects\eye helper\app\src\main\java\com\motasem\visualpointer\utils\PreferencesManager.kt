package com.motasem.visualpointer.utils

import android.content.Context
import android.content.SharedPreferences

/**
 * Manager for handling app preferences and settings
 */
class PreferencesManager(context: Context) {

    companion object {
        private const val PREFS_NAME = "visual_pointer_prefs"
        
        // Sensitivity Settings
        private const val KEY_EYE_TRACKING_SENSITIVITY = "eye_tracking_sensitivity"
        private const val KEY_BLINK_SENSITIVITY = "blink_sensitivity"
        private const val KEY_CURSOR_SPEED = "cursor_speed"
        private const val KEY_BLINK_DURATION = "blink_duration"
        
        // Visual Settings
        private const val KEY_CURSOR_SIZE = "cursor_size"
        private const val KEY_CURSOR_COLOR = "cursor_color"
        private const val KEY_SHOW_FACE_OUTLINE = "show_face_outline"
        
        // Calibration Settings
        private const val KEY_CALIBRATION_DATA = "calibration_data"
        private const val KEY_IS_CALIBRATED = "is_calibrated"
        
        // Default Values
        private const val DEFAULT_EYE_TRACKING_SENSITIVITY = 50
        private const val DEFAULT_BLINK_SENSITIVITY = 60
        private const val DEFAULT_CURSOR_SPEED = 70
        private const val DEFAULT_BLINK_DURATION = 40
        private const val DEFAULT_CURSOR_SIZE = 50
        private const val DEFAULT_CURSOR_COLOR = "#FF4081"
        private const val DEFAULT_SHOW_FACE_OUTLINE = true
    }

    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

    // Eye Tracking Sensitivity (0-100)
    fun getEyeTrackingSensitivity(): Int {
        return sharedPreferences.getInt(KEY_EYE_TRACKING_SENSITIVITY, DEFAULT_EYE_TRACKING_SENSITIVITY)
    }

    fun setEyeTrackingSensitivity(sensitivity: Int) {
        sharedPreferences.edit()
            .putInt(KEY_EYE_TRACKING_SENSITIVITY, sensitivity.coerceIn(0, 100))
            .apply()
    }

    // Blink Detection Sensitivity (0-100)
    fun getBlinkSensitivity(): Int {
        return sharedPreferences.getInt(KEY_BLINK_SENSITIVITY, DEFAULT_BLINK_SENSITIVITY)
    }

    fun setBlinkSensitivity(sensitivity: Int) {
        sharedPreferences.edit()
            .putInt(KEY_BLINK_SENSITIVITY, sensitivity.coerceIn(0, 100))
            .apply()
    }

    // Cursor Speed (0-100)
    fun getCursorSpeed(): Int {
        return sharedPreferences.getInt(KEY_CURSOR_SPEED, DEFAULT_CURSOR_SPEED)
    }

    fun setCursorSpeed(speed: Int) {
        sharedPreferences.edit()
            .putInt(KEY_CURSOR_SPEED, speed.coerceIn(0, 100))
            .apply()
    }

    // Blink Duration Threshold (0-100)
    fun getBlinkDuration(): Int {
        return sharedPreferences.getInt(KEY_BLINK_DURATION, DEFAULT_BLINK_DURATION)
    }

    fun setBlinkDuration(duration: Int) {
        sharedPreferences.edit()
            .putInt(KEY_BLINK_DURATION, duration.coerceIn(0, 100))
            .apply()
    }

    // Cursor Size (0-100)
    fun getCursorSize(): Int {
        return sharedPreferences.getInt(KEY_CURSOR_SIZE, DEFAULT_CURSOR_SIZE)
    }

    fun setCursorSize(size: Int) {
        sharedPreferences.edit()
            .putInt(KEY_CURSOR_SIZE, size.coerceIn(0, 100))
            .apply()
    }

    // Cursor Color
    fun getCursorColor(): String {
        return sharedPreferences.getString(KEY_CURSOR_COLOR, DEFAULT_CURSOR_COLOR) ?: DEFAULT_CURSOR_COLOR
    }

    fun setCursorColor(color: String) {
        sharedPreferences.edit()
            .putString(KEY_CURSOR_COLOR, color)
            .apply()
    }

    // Show Face Outline
    fun getShowFaceOutline(): Boolean {
        return sharedPreferences.getBoolean(KEY_SHOW_FACE_OUTLINE, DEFAULT_SHOW_FACE_OUTLINE)
    }

    fun setShowFaceOutline(show: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_SHOW_FACE_OUTLINE, show)
            .apply()
    }

    // Calibration Data
    fun getCalibrationData(): String? {
        return sharedPreferences.getString(KEY_CALIBRATION_DATA, null)
    }

    fun setCalibrationData(data: String) {
        sharedPreferences.edit()
            .putString(KEY_CALIBRATION_DATA, data)
            .putBoolean(KEY_IS_CALIBRATED, true)
            .apply()
    }

    // Is Calibrated
    fun isCalibrated(): Boolean {
        return sharedPreferences.getBoolean(KEY_IS_CALIBRATED, false)
    }

    fun clearCalibration() {
        sharedPreferences.edit()
            .remove(KEY_CALIBRATION_DATA)
            .putBoolean(KEY_IS_CALIBRATED, false)
            .apply()
    }

    // Reset all settings to defaults
    fun resetToDefaults() {
        sharedPreferences.edit()
            .putInt(KEY_EYE_TRACKING_SENSITIVITY, DEFAULT_EYE_TRACKING_SENSITIVITY)
            .putInt(KEY_BLINK_SENSITIVITY, DEFAULT_BLINK_SENSITIVITY)
            .putInt(KEY_CURSOR_SPEED, DEFAULT_CURSOR_SPEED)
            .putInt(KEY_BLINK_DURATION, DEFAULT_BLINK_DURATION)
            .putInt(KEY_CURSOR_SIZE, DEFAULT_CURSOR_SIZE)
            .putString(KEY_CURSOR_COLOR, DEFAULT_CURSOR_COLOR)
            .putBoolean(KEY_SHOW_FACE_OUTLINE, DEFAULT_SHOW_FACE_OUTLINE)
            .apply()
    }

    // Convert percentage values to actual thresholds
    fun getBlinkThreshold(): Float {
        val sensitivity = getBlinkSensitivity()
        // Convert 0-100 to 0.3-0.8 (lower sensitivity = higher threshold)
        return 0.8f - (sensitivity / 100f * 0.5f)
    }

    fun getCursorSpeedMultiplier(): Float {
        val speed = getCursorSpeed()
        // Convert 0-100 to 0.5-2.0
        return 0.5f + (speed / 100f * 1.5f)
    }

    fun getBlinkDurationThreshold(): Long {
        val duration = getBlinkDuration()
        // Convert 0-100 to 100-500ms
        return 100L + (duration * 4L)
    }

    fun getCursorSizePixels(): Int {
        val size = getCursorSize()
        // Convert 0-100 to 16-64 pixels
        return 16 + (size * 48 / 100)
    }
}
