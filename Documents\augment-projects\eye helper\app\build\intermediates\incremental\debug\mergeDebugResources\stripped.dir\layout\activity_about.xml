<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@android:color/white"
    tools:context=".ui.AboutActivity">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@android:color/holo_blue_dark"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center_horizontal">

            <!-- App Icon and Title -->
            <ImageView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_marginBottom="16dp"
                android:src="@drawable/ic_eye"
                android:contentDescription="أيقونة التطبيق" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="المؤشر البصري"
                android:textColor="@android:color/holo_blue_dark"
                android:textSize="24sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/appVersionText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="الإصدار: 1.0"
                android:textColor="@android:color/darker_gray"
                android:textSize="14sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="32dp"
                android:gravity="center"
                android:text="تطبيق متطور للتحكم بالجهاز باستخدام تتبع الوجه ثلاثي الأبعاد\nمصمم خصيصاً لذوي الاحتياجات الخاصة"
                android:textColor="@android:color/black"
                android:textSize="16sp" />

            <!-- Developer Info -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="👨‍💻 معلومات المطور"
                android:textColor="@android:color/holo_blue_dark"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="الاسم: معتصم"
                android:textColor="@android:color/black"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="التخصص: مطور تطبيقات أندرويد"
                android:textColor="@android:color/black"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="32dp"
                android:text="الخبرة: تطوير تطبيقات الذكاء الاصطناعي والرؤية الحاسوبية"
                android:textColor="@android:color/black"
                android:textSize="16sp" />

            <!-- Contact Info -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="📞 تواصل معنا"
                android:textColor="@android:color/holo_blue_dark"
                android:textSize="18sp"
                android:textStyle="bold" />

            <!-- WhatsApp Contact -->
            <LinearLayout
                android:id="@+id/whatsappContactCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:background="@android:drawable/btn_default"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:text="📱"
                    android:textSize="24sp" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="واتساب"
                        android:textColor="@android:color/black"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="01062606098"
                        android:textColor="@android:color/darker_gray"
                        android:textSize="14sp" />

                </LinearLayout>

            </LinearLayout>

            <!-- Email Contact -->
            <LinearLayout
                android:id="@+id/emailContactCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:background="@android:drawable/btn_default"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:text="📧"
                    android:textSize="24sp" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="البريد الإلكتروني"
                        android:textColor="@android:color/black"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="<EMAIL>"
                        android:textColor="@android:color/darker_gray"
                        android:textSize="14sp" />

                </LinearLayout>

            </LinearLayout>

            <!-- Additional Actions -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:text="🔗 روابط مفيدة"
                android:textColor="@android:color/holo_blue_dark"
                android:textSize="18sp"
                android:textStyle="bold" />

            <!-- GitHub Link -->
            <LinearLayout
                android:id="@+id/githubLinkCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:background="@android:drawable/btn_default"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:text="💻"
                    android:textSize="24sp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="GitHub - مشاريع المطور الأخرى"
                    android:textColor="@android:color/black"
                    android:textSize="16sp"
                    android:textStyle="bold" />

            </LinearLayout>

            <!-- Rate App -->
            <LinearLayout
                android:id="@+id/rateAppCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:background="@android:drawable/btn_default"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:text="⭐"
                    android:textSize="24sp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="قيم التطبيق في متجر التطبيقات"
                    android:textColor="@android:color/black"
                    android:textSize="16sp"
                    android:textStyle="bold" />

            </LinearLayout>

            <!-- Share App -->
            <LinearLayout
                android:id="@+id/shareAppCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:background="@android:drawable/btn_default"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:text="📤"
                    android:textSize="24sp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="شارك التطبيق مع الأصدقاء"
                    android:textColor="@android:color/black"
                    android:textSize="16sp"
                    android:textStyle="bold" />

            </LinearLayout>

            <!-- Privacy Policy -->
            <LinearLayout
                android:id="@+id/privacyPolicyCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:background="@android:drawable/btn_default"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:text="🔒"
                    android:textSize="24sp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="سياسة الخصوصية - كيف نحمي بياناتك"
                    android:textColor="@android:color/black"
                    android:textSize="16sp"
                    android:textStyle="bold" />

            </LinearLayout>

            <!-- Footer -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="32dp"
                android:gravity="center"
                android:text="صُنع بـ ❤️ في مصر\n© 2024 معتصم - جميع الحقوق محفوظة"
                android:textColor="@android:color/darker_gray"
                android:textSize="12sp" />

        </LinearLayout>

    </ScrollView>

</LinearLayout>


