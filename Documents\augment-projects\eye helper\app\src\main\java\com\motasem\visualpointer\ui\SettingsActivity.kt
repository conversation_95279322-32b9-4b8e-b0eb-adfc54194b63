package com.motasem.visualpointer.ui

import android.content.SharedPreferences
import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.motasem.visualpointer.R
import com.motasem.visualpointer.databinding.ActivitySettingsBinding
import com.motasem.visualpointer.utils.PreferencesManager

class SettingsActivity : AppCompatActivity() {

    private lateinit var binding: ActivitySettingsBinding
    private lateinit var preferencesManager: PreferencesManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        preferencesManager = PreferencesManager(this)
        
        setupUI()
        loadSettings()
    }

    private fun setupUI() {
        binding.saveSettingsButton.setOnClickListener {
            saveSettings()
        }

        binding.resetSettingsButton.setOnClickListener {
            resetSettings()
        }

        binding.backButton.setOnClickListener {
            finish()
        }
    }

    private fun loadSettings() {
        binding.eyeTrackingSensitivitySeekBar.progress = preferencesManager.getEyeTrackingSensitivity()
        binding.blinkSensitivitySeekBar.progress = preferencesManager.getBlinkSensitivity()
        binding.cursorSpeedSeekBar.progress = preferencesManager.getCursorSpeed()
        binding.blinkDurationSeekBar.progress = preferencesManager.getBlinkDuration()
        binding.cursorSizeSeekBar.progress = preferencesManager.getCursorSize()
        binding.showFaceOutlineSwitch.isChecked = preferencesManager.getShowFaceOutline()
    }

    private fun saveSettings() {
        preferencesManager.setEyeTrackingSensitivity(binding.eyeTrackingSensitivitySeekBar.progress)
        preferencesManager.setBlinkSensitivity(binding.blinkSensitivitySeekBar.progress)
        preferencesManager.setCursorSpeed(binding.cursorSpeedSeekBar.progress)
        preferencesManager.setBlinkDuration(binding.blinkDurationSeekBar.progress)
        preferencesManager.setCursorSize(binding.cursorSizeSeekBar.progress)
        preferencesManager.setShowFaceOutline(binding.showFaceOutlineSwitch.isChecked)

        Toast.makeText(this, "تم حفظ الإعدادات", Toast.LENGTH_SHORT).show()
        finish()
    }

    private fun resetSettings() {
        preferencesManager.resetToDefaults()
        loadSettings()
        Toast.makeText(this, "تم إعادة تعيين الإعدادات", Toast.LENGTH_SHORT).show()
    }
}
