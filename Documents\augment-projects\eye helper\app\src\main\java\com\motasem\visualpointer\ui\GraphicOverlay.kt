package com.motasem.visualpointer.ui

import android.content.Context
import android.content.res.Configuration
import android.graphics.Canvas
import android.graphics.Rect
import android.graphics.RectF
import android.util.AttributeSet
import android.view.View
import kotlin.math.ceil

/**
 * A view group that displays graphics over a camera preview
 */
open class GraphicOverlay(context: Context?, attrs: AttributeSet?) : View(context, attrs) {
    
    private val lock = Any()
    private val graphics: MutableList<Graphic> = ArrayList()
    
    var mScale: Float? = null
    var mOffsetX: Float? = null
    var mOffsetY: Float? = null

    /**
     * Base class for graphics that can be drawn on the overlay
     */
    abstract class Graphic(private val overlay: GraphicOverlay) {

        abstract fun draw(canvas: Canvas?)

        /**
         * Calculates the rectangle coordinates for drawing on the overlay
         */
        fun calculateRect(height: Float, width: Float, boundingBoxT: Rect): RectF {
            
            fun isLandScapeMode(): Boolean {
                return overlay.context.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE
            }

            fun whenLandScapeModeWidth(): Float {
                return when (isLandScapeMode()) {
                    true -> width
                    false -> height
                }
            }

            fun whenLandScapeModeHeight(): Float {
                return when (isLandScapeMode()) {
                    true -> height
                    false -> width
                }
            }

            val scaleX = overlay.width.toFloat() / whenLandScapeModeWidth()
            val scaleY = overlay.height.toFloat() / whenLandScapeModeHeight()
            val scale = scaleX.coerceAtLeast(scaleY)
            overlay.mScale = scale

            // Calculate offset (we need to center the overlay on the target)
            val offsetX = (overlay.width.toFloat() - ceil(whenLandScapeModeWidth() * scale)) / 2.0f
            val offsetY = (overlay.height.toFloat() - ceil(whenLandScapeModeHeight() * scale)) / 2.0f

            overlay.mOffsetX = offsetX
            overlay.mOffsetY = offsetY

            val mappedBox = RectF().apply {
                left = boundingBoxT.right * scale + offsetX
                top = boundingBoxT.top * scale + offsetY
                right = boundingBoxT.left * scale + offsetX
                bottom = boundingBoxT.bottom * scale + offsetY
            }
            return mappedBox
        }
    }

    /**
     * Removes all graphics from the overlay
     */
    fun clear() {
        synchronized(lock) { 
            graphics.clear() 
        }
        postInvalidate()
    }

    /**
     * Adds a graphic to the overlay
     */
    fun add(graphic: Graphic) {
        synchronized(lock) { 
            graphics.add(graphic) 
        }
    }

    /**
     * Draws all graphics on the canvas
     */
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        synchronized(lock) {
            for (graphic in graphics) {
                graphic.draw(canvas)
            }
        }
    }
}
