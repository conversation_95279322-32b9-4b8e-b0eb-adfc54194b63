# المؤشر البصري (Visual Pointer)

تطبيق أندرويد مبتكر للتحكم في الأجهزة عبر تتبع حركة العين والغمزات، مصمم خصيصاً للأشخاص ذوي الإعاقات الحركية.

## المطور
**معتصم سالم (Motasem Salem)**  
للتواصل والاستفسار: واتساب - 01062606098

## نظرة عامة

يستخدم تطبيق "المؤشر البصري" تقنيات الذكاء الاصطناعي والرؤية الحاسوبية لتمكين المستخدمين من التحكم في هواتفهم الذكية بدون الحاجة إلى اللمس المباشر. يعتمد التطبيق على:

- **Google ML Kit** لتتبع الوجه واكتشاف حركة العين
- **CameraX** لمعالجة بث الفيديو المباشر
- **Accessibility Services** للتحكم في النظام

## الميزات الرئيسية

### 🎯 تتبع العين الدقيق
- تتبع حركة العين في الوقت الفعلي
- دقة عالية في ظروف الإضاءة الجيدة
- مؤشر بصري يتحرك مع نظر المستخدم

### 👁️ التحكم بالغمزات
- **غمزة العين اليمنى**: تنفيذ نقرة
- **غمزة العين اليسرى**: الرجوع للخلف
- **غمزة كلتا العينين**: فتح قائمة التحكم المتقدمة

### ⚙️ إعدادات قابلة للتخصيص
- حساسية تتبع العين
- حساسية اكتشاف الغمز
- سرعة المؤشر
- حجم ولون المؤشر
- مدة الغمزة المطلوبة

### 🎯 نظام معايرة متقدم
- معايرة شخصية لكل مستخدم
- تحسين دقة التتبع
- واجهة معايرة سهلة الاستخدام

## المتطلبات التقنية

### متطلبات النظام
- **Android 8.0 (API 26)** أو أحدث
- **كاميرا أمامية** عالية الجودة
- **ذاكرة RAM**: 3 جيجابايت أو أكثر
- **مساحة تخزين**: 100 ميجابايت

### الصلاحيات المطلوبة
- **الكاميرا**: لتتبع حركة العين
- **العرض فوق التطبيقات**: لإظهار المؤشر العائم
- **خدمة إمكانية الوصول**: للتحكم في النظام

## التقنيات المستخدمة

### المكتبات الأساسية
```gradle
// ML Kit Face Detection
implementation 'com.google.android.gms:play-services-mlkit-face-detection:17.1.0'

// CameraX
implementation "androidx.camera:camera-core:1.3.1"
implementation "androidx.camera:camera-camera2:1.3.1"
implementation "androidx.camera:camera-lifecycle:1.3.1"
implementation "androidx.camera:camera-view:1.3.1"
```

### البنية المعمارية
- **MVVM Pattern**: لفصل منطق العمل عن واجهة المستخدم
- **Repository Pattern**: لإدارة البيانات
- **Observer Pattern**: للتواصل بين المكونات

## هيكل المشروع

```
app/
├── src/main/java/com/motasem/visualpointer/
│   ├── MainActivity.kt                 # الشاشة الرئيسية
│   ├── camera/
│   │   ├── CameraManager.kt           # إدارة الكاميرا
│   │   ├── FaceDetectionAnalyzer.kt   # تحليل الوجه
│   │   └── Analyzer.kt                # محلل الصور الأساسي
│   ├── service/
│   │   ├── EyeTrackingService.kt      # خدمة تتبع العين
│   │   └── VisualPointerAccessibilityService.kt # خدمة إمكانية الوصول
│   ├── ui/
│   │   ├── GraphicOverlay.kt          # طبقة الرسم
│   │   ├── FaceGraphic.kt             # رسم حدود الوجه
│   │   ├── SettingsActivity.kt        # شاشة الإعدادات
│   │   └── CalibrationActivity.kt     # شاشة المعايرة
│   ├── model/
│   │   └── TrackingStatus.kt          # نماذج البيانات
│   └── utils/
│       └── PreferencesManager.kt      # إدارة الإعدادات
└── src/main/res/
    ├── layout/                        # ملفات التخطيط
    ├── values/                        # القيم والألوان
    ├── drawable/                      # الرسوميات
    └── xml/                          # ملفات التكوين
```

## كيفية الاستخدام

### 1. التثبيت والإعداد
1. قم بتثبيت التطبيق على جهازك
2. امنح الصلاحيات المطلوبة (الكاميرا، العرض، إمكانية الوصول)
3. قم بتشغيل التطبيق واتبع التعليمات

### 2. المعايرة الأولى
1. اضغط على "المعايرة" في الشاشة الرئيسية
2. انظر إلى النقاط التي تظهر على الشاشة بالترتيب
3. انتظر حتى اكتمال عملية المعايرة

### 3. بدء الاستخدام
1. اضغط على "بدء التتبع"
2. ستظهر نقطة حمراء تتحرك مع نظرك
3. استخدم الغمزات للتحكم:
   - غمزة يمين = نقر
   - غمزة يسار = رجوع
   - غمزة كلتا العينين = قائمة متقدمة

## نصائح للاستخدام الأمثل

### 🔆 الإضاءة
- استخدم إضاءة جيدة ومتوازنة
- تجنب الإضاءة الخلفية القوية
- تأكد من إضاءة وجهك بشكل متساوٍ

### 📱 وضعية الجهاز
- احتفظ بمسافة 30-50 سم من الشاشة
- ضع الجهاز في مستوى العين
- تجنب الحركة المفرطة أثناء الاستخدام

### ⚙️ الإعدادات
- اضبط الحساسية حسب احتياجاتك
- استخدم المعايرة عند تغيير وضعية الجلوس
- جرب إعدادات مختلفة للحصول على أفضل أداء

## استكشاف الأخطاء وإصلاحها

### مشاكل شائعة وحلولها

#### لا يتم اكتشاف الوجه
- تحقق من الإضاءة
- تأكد من وضوح الكاميرا
- اضبط المسافة من الشاشة

#### المؤشر غير دقيق
- قم بإعادة المعايرة
- اضبط حساسية تتبع العين
- تحقق من ثبات وضعية الرأس

#### الغمزات لا تعمل
- اضبط حساسية اكتشاف الغمز
- تأكد من وضوح العينين في الكاميرا
- جرب غمزات أكثر وضوحاً

## الأمان والخصوصية

### 🔒 حماية البيانات
- جميع بيانات الكاميرا تُعالج محلياً على الجهاز
- لا يتم إرسال أي بيانات لخوادم خارجية
- لا يتم حفظ صور أو فيديوهات

### 🛡️ الصلاحيات
- الكاميرا: فقط لتتبع العين
- العرض: فقط لإظهار المؤشر
- إمكانية الوصول: فقط للتحكم في النظام

## التطوير المستقبلي

### ميزات مخططة
- [ ] دعم إيماءات الرأس
- [ ] تحسين الأداء في الإضاءة المنخفضة
- [ ] لوحة مفاتيح افتراضية
- [ ] دعم أوامر صوتية
- [ ] واجهة مستخدم محسنة

### تحسينات تقنية
- [ ] تحسين خوارزمية تتبع العين
- [ ] تقليل استهلاك البطارية
- [ ] دعم المزيد من أحجام الشاشات
- [ ] تحسين دقة اكتشاف الغمزات

## المساهمة في المشروع

نرحب بالمساهمات من المطورين لتحسين التطبيق:

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## التواصل والدعم

**المطور**: معتصم سالم  
**واتساب**: 01062606098  
**البريد الإلكتروني**: [البريد الإلكتروني]

---

**تم تطوير هذا التطبيق بهدف تحسين جودة الحياة للأشخاص ذوي الإعاقات الحركية وتمكينهم من الوصول الكامل للتكنولوجيا الرقمية.**
