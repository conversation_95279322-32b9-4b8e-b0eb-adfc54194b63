package com.motasem.visualpointer.camera

import android.content.Context
import android.util.Log
import androidx.camera.core.Camera
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.google.mlkit.vision.face.Face
import com.motasem.visualpointer.model.TrackingStatus
import com.motasem.visualpointer.ui.GraphicOverlay
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

/**
 * Camera manager for handling camera operations and face detection
 */
class CameraManager(
    private val context: Context,
    private val previewView: PreviewView,
    private val lifecycleOwner: LifecycleOwner,
    private val graphicOverlay: GraphicOverlay,
    private val listener: (TrackingStatus, Face?) -> Unit
) {

    companion object {
        private const val TAG = "CameraManager"
    }

    private var preview: Preview? = null
    private var camera: Camera? = null
    private lateinit var cameraExecutor: ExecutorService
    private var cameraProvider: ProcessCameraProvider? = null
    private var imageAnalyzer: ImageAnalysis? = null
    
    // Use front camera for eye tracking
    private val cameraSelectorOption = CameraSelector.LENS_FACING_FRONT

    init {
        createNewExecutor()
    }

    private fun createNewExecutor() {
        cameraExecutor = Executors.newSingleThreadExecutor()
    }

    /**
     * Start the camera and begin face detection
     */
    fun startCamera() {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(context)
        cameraProviderFuture.addListener(
            {
                try {
                    cameraProvider = cameraProviderFuture.get()
                    preview = Preview.Builder().build()

                    imageAnalyzer = ImageAnalysis.Builder()
                        .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                        .build()
                        .also {
                            it.setAnalyzer(cameraExecutor, selectAnalyzer())
                        }

                    val cameraSelector = CameraSelector.Builder()
                        .requireLensFacing(cameraSelectorOption)
                        .build()

                    setCameraConfig(cameraProvider, cameraSelector)
                    
                } catch (exc: Exception) {
                    Log.e(TAG, "Use case binding failed", exc)
                    listener(TrackingStatus.ERROR, null)
                }
            }, 
            ContextCompat.getMainExecutor(context)
        )
    }

    /**
     * Stop the camera and release resources
     */
    fun stopCamera() {
        cameraProvider?.unbindAll()
        if (::cameraExecutor.isInitialized) {
            cameraExecutor.shutdown()
        }
    }

    /**
     * Select the appropriate analyzer for face detection
     */
    private fun selectAnalyzer(): ImageAnalysis.Analyzer {
        return FaceDetectionAnalyzer(graphicOverlay, listener)
    }

    /**
     * Configure camera with preview and analysis use cases
     */
    private fun setCameraConfig(
        cameraProvider: ProcessCameraProvider?,
        cameraSelector: CameraSelector
    ) {
        try {
            // Unbind use cases before rebinding
            cameraProvider?.unbindAll()

            // Bind use cases to camera
            camera = cameraProvider?.bindToLifecycle(
                lifecycleOwner,
                cameraSelector,
                preview,
                imageAnalyzer
            )

            // Attach the viewfinder's surface provider to preview use case
            preview?.setSurfaceProvider(previewView.surfaceProvider)
            
            Log.d(TAG, "Camera started successfully")
            
        } catch (exc: Exception) {
            Log.e(TAG, "Use case binding failed", exc)
            listener(TrackingStatus.ERROR, null)
        }
    }

    /**
     * Check if camera is available
     */
    fun isCameraAvailable(): Boolean {
        return try {
            val cameraProvider = ProcessCameraProvider.getInstance(context).get()
            cameraProvider.hasCamera(
                CameraSelector.Builder()
                    .requireLensFacing(cameraSelectorOption)
                    .build()
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error checking camera availability", e)
            false
        }
    }
}
