<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.VisualPointer" parent="Theme.Material3.DayNight">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/accent</item>
        <item name="colorSecondaryVariant">@color/accent_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        
        <!-- Background colors -->
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorOnSurface">@color/text_primary</item>
        
        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/primary_dark</item>
        
        <!-- Customize your theme here. -->
        <item name="android:windowContentTransitions">true</item>
    </style>

    <!-- Splash Screen Theme -->
    <style name="Theme.VisualPointer.Splash" parent="Theme.VisualPointer">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
    </style>

    <!-- Fullscreen Theme for Camera -->
    <style name="Theme.VisualPointer.Fullscreen" parent="Theme.VisualPointer">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <!-- Card Style -->
    <style name="CardStyle">
        <item name="android:layout_margin">16dp</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:background">@color/card_background</item>
    </style>

    <!-- Button Styles -->
    <style name="PrimaryButton" parent="Widget.Material3.Button">
        <item name="android:layout_height">56dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="cornerRadius">28dp</item>
    </style>

    <style name="SecondaryButton" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:layout_height">56dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:textSize">16sp</item>
        <item name="cornerRadius">28dp</item>
    </style>

    <!-- Text Styles -->
    <style name="TitleText">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:layout_margin">16dp</item>
    </style>

    <style name="SubtitleText">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:layout_margin">8dp</item>
    </style>

    <style name="StatusText">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:padding">8dp</item>
        <item name="android:background">@drawable/status_background</item>
        <item name="android:textColor">@color/white</item>
    </style>
</resources>
