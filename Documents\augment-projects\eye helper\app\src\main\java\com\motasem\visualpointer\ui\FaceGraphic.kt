package com.motasem.visualpointer.ui

import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Rect
import com.google.mlkit.vision.face.Face
import com.motasem.visualpointer.model.TrackingStatus

/**
 * Graphic instance for rendering face detection results
 */
class FaceGraphic(
    overlay: GraphicOverlay,
    private val face: Face,
    private val imageRect: Rect,
    private val listener: (TrackingStatus, Face?) -> Unit
) : GraphicOverlay.Graphic(overlay) {

    private val facePositionPaint: Paint
    private val idPaint: Paint
    private val boxPaint: Paint

    init {
        val selectedColor = Color.WHITE
        
        facePositionPaint = Paint().apply {
            color = selectedColor
        }
        
        idPaint = Paint().apply {
            color = selectedColor
            textSize = 40f
        }
        
        boxPaint = Paint().apply {
            color = selectedColor
            style = Paint.Style.STROKE
            strokeWidth = 5.0f
        }
    }

    private val greenBoxPaint = Paint().apply {
        color = Color.GREEN
        style = Paint.Style.STROKE
        strokeWidth = 5.0f
    }

    private val redBoxPaint = Paint().apply {
        color = Color.RED
        style = Paint.Style.STROKE
        strokeWidth = 5.0f
    }

    override fun draw(canvas: Canvas?) {
        val rect = calculateRect(
            imageRect.height().toFloat(),
            imageRect.width().toFloat(),
            face.boundingBox
        )
        
        val leftEyeProbability = getLeftEyeProbability()
        val rightEyeProbability = getRightEyeProbability()
        
        // Determine face box color based on eye states
        val paintToUse = when {
            leftEyeProbability <= 0.6f || rightEyeProbability <= 0.6f -> redBoxPaint
            else -> greenBoxPaint
        }
        
        // Draw face bounding box
        canvas?.drawRect(rect, paintToUse)
        
        // Draw face ID if available
        face.trackingId?.let { trackingId ->
            canvas?.drawText(
                "ID: $trackingId",
                rect.left,
                rect.top - 10f,
                idPaint
            )
        }
        
        // Draw eye probabilities for debugging
        canvas?.drawText(
            "L: ${String.format("%.2f", leftEyeProbability)}",
            rect.left,
            rect.bottom + 30f,
            idPaint
        )
        
        canvas?.drawText(
            "R: ${String.format("%.2f", rightEyeProbability)}",
            rect.left,
            rect.bottom + 70f,
            idPaint
        )
    }

    private fun getLeftEyeProbability(): Float {
        return face.leftEyeOpenProbability ?: 1.0f
    }

    private fun getRightEyeProbability(): Float {
        return face.rightEyeOpenProbability ?: 1.0f
    }
}
