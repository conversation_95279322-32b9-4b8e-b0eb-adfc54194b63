{"logs": [{"outputFile": "com.motasem.visualpointer.app-mergeDebugResources-41:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ec7b75c409fae5c6e754c810938a9a33\\transformed\\appcompat-1.6.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,2835", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,2912"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "432,552,655,771,857,962,1081,1161,1238,1330,1424,1519,1613,1708,1802,1898,1993,2085,2177,2258,2364,2469,2567,2675,2781,2889,3062,11728", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "547,650,766,852,957,1076,1156,1233,1325,1419,1514,1608,1703,1797,1893,1988,2080,2172,2253,2359,2464,2562,2670,2776,2884,3057,3157,11805"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\eb17ecbf9872120b924d2bbfd7cfe0e6\\transformed\\core-1.12.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "40,41,42,43,44,45,46,129", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3587,3685,3787,3887,3988,4094,4197,11810", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "3680,3782,3882,3983,4089,4192,4313,11906"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9f08e0d719849b14ed4ab8905bd80cf7\\transformed\\play-services-base-18.1.0\\res\\values-be\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,454,575,681,832,954,1065,1165,1323,1426,1585,1709,1858,2013,2078,2136", "endColumns": "102,157,120,105,150,121,110,99,157,102,158,123,148,154,64,57,74", "endOffsets": "295,453,574,680,831,953,1064,1164,1322,1425,1584,1708,1857,2012,2077,2135,2210"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4630,4737,4899,5024,5134,5289,5415,5530,5780,5942,6049,6212,6340,6493,6652,6721,6783", "endColumns": "106,161,124,109,154,125,114,103,161,106,162,127,152,158,68,61,78", "endOffsets": "4732,4894,5019,5129,5284,5410,5525,5629,5937,6044,6207,6335,6488,6647,6716,6778,6857"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\949294b35df779989b974dc81fe3e123\\transformed\\play-services-basement-18.1.0\\res\\values-be\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5634", "endColumns": "145", "endOffsets": "5775"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\06fd2fa39dd57ce426fb9e8f08397def\\transformed\\material-1.11.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,382,459,536,618,715,807,904,1036,1119,1186,1279,1356,1419,1535,1598,1667,1726,1797,1856,1910,2031,2092,2155,2209,2282,2404,2492,2575,2727,2813,2900,3033,3124,3207,3264,3315,3381,3453,3530,3614,3697,3772,3849,3931,4007,4115,4204,4286,4377,4473,4547,4628,4723,4777,4859,4925,5012,5098,5160,5224,5287,5356,5466,5579,5682,5789,5850,5905", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,76,76,81,96,91,96,131,82,66,92,76,62,115,62,68,58,70,58,53,120,60,62,53,72,121,87,82,151,85,86,132,90,82,56,50,65,71,76,83,82,74,76,81,75,107,88,81,90,95,73,80,94,53,81,65,86,85,61,63,62,68,109,112,102,106,60,54,79", "endOffsets": "377,454,531,613,710,802,899,1031,1114,1181,1274,1351,1414,1530,1593,1662,1721,1792,1851,1905,2026,2087,2150,2204,2277,2399,2487,2570,2722,2808,2895,3028,3119,3202,3259,3310,3376,3448,3525,3609,3692,3767,3844,3926,4002,4110,4199,4281,4372,4468,4542,4623,4718,4772,4854,4920,5007,5093,5155,5219,5282,5351,5461,5574,5677,5784,5845,5900,5980"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3162,3239,3316,3398,3495,4318,4415,4547,6862,6929,7022,7099,7162,7278,7341,7410,7469,7540,7599,7653,7774,7835,7898,7952,8025,8147,8235,8318,8470,8556,8643,8776,8867,8950,9007,9058,9124,9196,9273,9357,9440,9515,9592,9674,9750,9858,9947,10029,10120,10216,10290,10371,10466,10520,10602,10668,10755,10841,10903,10967,11030,11099,11209,11322,11425,11532,11593,11648", "endLines": "7,35,36,37,38,39,47,48,49,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127", "endColumns": "12,76,76,81,96,91,96,131,82,66,92,76,62,115,62,68,58,70,58,53,120,60,62,53,72,121,87,82,151,85,86,132,90,82,56,50,65,71,76,83,82,74,76,81,75,107,88,81,90,95,73,80,94,53,81,65,86,85,61,63,62,68,109,112,102,106,60,54,79", "endOffsets": "427,3234,3311,3393,3490,3582,4410,4542,4625,6924,7017,7094,7157,7273,7336,7405,7464,7535,7594,7648,7769,7830,7893,7947,8020,8142,8230,8313,8465,8551,8638,8771,8862,8945,9002,9053,9119,9191,9268,9352,9435,9510,9587,9669,9745,9853,9942,10024,10115,10211,10285,10366,10461,10515,10597,10663,10750,10836,10898,10962,11025,11094,11204,11317,11420,11527,11588,11643,11723"}}]}]}