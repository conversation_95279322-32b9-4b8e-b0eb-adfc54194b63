package com.motasem.visualpointer.ui

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Context
import android.graphics.PixelFormat
import android.graphics.drawable.GradientDrawable
import android.os.Build
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.view.animation.AccelerateDecelerateInterpolator
import androidx.core.content.ContextCompat
import com.motasem.visualpointer.R
import com.motasem.visualpointer.databinding.OverlayCursorBinding
import com.motasem.visualpointer.utils.PreferencesManager

/**
 * Advanced floating cursor with animations and customization
 */
class FloatingCursor(private val context: Context) {

    companion object {
        private const val TAG = "FloatingCursor"
        private const val ANIMATION_DURATION = 200L
        private const val PULSE_DURATION = 1000L
    }

    private val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    private val preferencesManager = PreferencesManager(context)
    private lateinit var binding: OverlayCursorBinding
    private var layoutParams: WindowManager.LayoutParams? = null
    private var isShown = false
    private var currentAnimator: AnimatorSet? = null

    init {
        createCursorView()
    }

    private fun createCursorView() {
        binding = OverlayCursorBinding.inflate(LayoutInflater.from(context))
        setupCursorAppearance()
        createLayoutParams()
    }

    private fun setupCursorAppearance() {
        val cursorSize = preferencesManager.getCursorSizePixels()
        val cursorColor = preferencesManager.getCursorColor()

        // Update cursor size
        val layoutParams = binding.cursorView.layoutParams
        layoutParams.width = cursorSize
        layoutParams.height = cursorSize
        binding.cursorView.layoutParams = layoutParams

        // Update cursor color
        try {
            val drawable = ContextCompat.getDrawable(context, R.drawable.cursor_circle) as? GradientDrawable
            drawable?.setColor(android.graphics.Color.parseColor(cursorColor))
            binding.cursorView.background = drawable
        } catch (e: Exception) {
            Log.w(TAG, "Failed to set cursor color: $cursorColor", e)
        }

        // Setup pulse ring
        val pulseSize = (cursorSize * 1.5f).toInt()
        val pulseLayoutParams = binding.pulseRing.layoutParams
        pulseLayoutParams.width = pulseSize
        pulseLayoutParams.height = pulseSize
        binding.pulseRing.layoutParams = pulseLayoutParams
    }

    private fun createLayoutParams() {
        layoutParams = WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                @Suppress("DEPRECATION")
                WindowManager.LayoutParams.TYPE_PHONE
            },
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE or
                    WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.TOP or Gravity.START
            x = 100
            y = 100
        }
    }

    /**
     * Show the floating cursor with enhanced error handling for Realme
     */
    fun show() {
        if (isShown) {
            Log.d(TAG, "Cursor already shown")
            return
        }

        try {
            Log.d(TAG, "محاولة إظهار المؤشر...")

            // تحقق من صلاحية العرض قبل المحاولة
            if (!android.provider.Settings.canDrawOverlays(context)) {
                Log.e(TAG, "❌ صلاحية العرض غير مفعلة")
                return
            }

            // إنشاء layoutParams جديد في كل مرة (حل لمشاكل Realme)
            createLayoutParams()

            windowManager.addView(binding.root, layoutParams)
            isShown = true

            // Animate entrance
            animateEntrance()

            Log.d(TAG, "✅ تم إظهار المؤشر بنجاح")

            // تحقق من الظهور الفعلي بعد ثانية
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                if (binding.root.isAttachedToWindow) {
                    Log.d(TAG, "✅ المؤشر مرفق بالنافذة بنجاح")
                } else {
                    Log.w(TAG, "⚠️ المؤشر غير مرفق بالنافذة")
                }
            }, 1000)

        } catch (e: SecurityException) {
            Log.e(TAG, "❌ خطأ أمني في إظهار المؤشر - تحقق من صلاحية العرض", e)
        } catch (e: android.view.WindowManager.BadTokenException) {
            Log.e(TAG, "❌ خطأ في token النافذة - سيتم المحاولة مرة أخرى", e)
            // محاولة مرة أخرى بعد ثانية
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                retryShow()
            }, 1000)
        } catch (e: Exception) {
            Log.e(TAG, "❌ خطأ عام في إظهار المؤشر", e)
        }
    }

    private fun retryShow() {
        Log.d(TAG, "إعادة محاولة إظهار المؤشر...")
        isShown = false // إعادة تعيين الحالة
        show()
    }

    /**
     * Hide the floating cursor
     */
    fun hide() {
        if (!isShown) return

        try {
            // Cancel any running animations
            currentAnimator?.cancel()
            
            windowManager.removeView(binding.root)
            isShown = false
            
            Log.d(TAG, "Floating cursor hidden")
        } catch (e: Exception) {
            Log.e(TAG, "Error hiding floating cursor", e)
        }
    }

    /**
     * Update cursor position with smooth animation
     */
    fun updatePosition(x: Float, y: Float, animate: Boolean = true) {
        if (!isShown || layoutParams == null) return

        try {
            val params = layoutParams!!
            val targetX = x.toInt() - binding.cursorView.width / 2
            val targetY = y.toInt() - binding.cursorView.height / 2

            if (animate) {
                animateToPosition(targetX, targetY)
            } else {
                params.x = targetX
                params.y = targetY
                windowManager.updateViewLayout(binding.root, params)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error updating cursor position", e)
        }
    }

    /**
     * Animate cursor to new position
     */
    private fun animateToPosition(targetX: Int, targetY: Int) {
        val params = layoutParams ?: return
        
        val startX = params.x
        val startY = params.y
        
        val animatorX = ObjectAnimator.ofInt(startX, targetX).apply {
            addUpdateListener { animation ->
                params.x = animation.animatedValue as Int
                try {
                    windowManager.updateViewLayout(binding.root, params)
                } catch (e: Exception) {
                    Log.w(TAG, "Error during position animation", e)
                }
            }
        }
        
        val animatorY = ObjectAnimator.ofInt(startY, targetY).apply {
            addUpdateListener { animation ->
                params.y = animation.animatedValue as Int
            }
        }

        currentAnimator?.cancel()
        currentAnimator = AnimatorSet().apply {
            playTogether(animatorX, animatorY)
            duration = ANIMATION_DURATION
            interpolator = AccelerateDecelerateInterpolator()
            start()
        }
    }

    /**
     * Animate cursor entrance
     */
    private fun animateEntrance() {
        binding.cursorView.alpha = 0f
        binding.cursorView.scaleX = 0.5f
        binding.cursorView.scaleY = 0.5f

        val fadeIn = ObjectAnimator.ofFloat(binding.cursorView, "alpha", 0f, 1f)
        val scaleX = ObjectAnimator.ofFloat(binding.cursorView, "scaleX", 0.5f, 1f)
        val scaleY = ObjectAnimator.ofFloat(binding.cursorView, "scaleY", 0.5f, 1f)

        AnimatorSet().apply {
            playTogether(fadeIn, scaleX, scaleY)
            duration = ANIMATION_DURATION
            interpolator = AccelerateDecelerateInterpolator()
            start()
        }
    }

    /**
     * Show click animation
     */
    fun animateClick() {
        if (!isShown) return

        // Scale animation for click feedback
        val scaleX = ObjectAnimator.ofFloat(binding.cursorView, "scaleX", 1f, 1.3f, 1f)
        val scaleY = ObjectAnimator.ofFloat(binding.cursorView, "scaleY", 1f, 1.3f, 1f)

        AnimatorSet().apply {
            playTogether(scaleX, scaleY)
            duration = 150L
            start()
        }

        // Show pulse ring
        showPulseAnimation()
    }

    /**
     * Show pulse animation for special actions
     */
    private fun showPulseAnimation() {
        binding.pulseRing.visibility = View.VISIBLE
        binding.pulseRing.alpha = 0.8f
        binding.pulseRing.scaleX = 0.5f
        binding.pulseRing.scaleY = 0.5f

        val fadeOut = ObjectAnimator.ofFloat(binding.pulseRing, "alpha", 0.8f, 0f)
        val scaleX = ObjectAnimator.ofFloat(binding.pulseRing, "scaleX", 0.5f, 1.5f)
        val scaleY = ObjectAnimator.ofFloat(binding.pulseRing, "scaleY", 0.5f, 1.5f)

        AnimatorSet().apply {
            playTogether(fadeOut, scaleX, scaleY)
            duration = PULSE_DURATION
            interpolator = AccelerateDecelerateInterpolator()
            start()
        }.doOnEnd {
            binding.pulseRing.visibility = View.GONE
        }
    }

    /**
     * Set cursor visibility
     */
    fun setVisible(visible: Boolean) {
        if (!isShown) return
        
        binding.cursorView.visibility = if (visible) View.VISIBLE else View.INVISIBLE
    }

    /**
     * Update cursor appearance based on current settings
     */
    fun updateAppearance() {
        setupCursorAppearance()
    }

    /**
     * Check if cursor is currently shown and actually visible
     */
    fun isVisible(): Boolean {
        val basicCheck = isShown
        val attachedCheck = try {
            binding.root.isAttachedToWindow
        } catch (e: Exception) {
            false
        }

        Log.d(TAG, "فحص المؤشر - أساسي: $basicCheck، مرفق: $attachedCheck")
        return basicCheck && attachedCheck
    }

    /**
     * Get current cursor position
     */
    fun getCurrentPosition(): Pair<Int, Int>? {
        return layoutParams?.let { Pair(it.x, it.y) }
    }

    /**
     * Comprehensive diagnostic information
     */
    fun getDiagnosticInfo(): String {
        val overlayPermission = android.provider.Settings.canDrawOverlays(context)
        val isAttached = try { binding.root.isAttachedToWindow } catch (e: Exception) { false }
        val hasLayoutParams = layoutParams != null
        val deviceBrand = android.os.Build.BRAND
        val deviceModel = android.os.Build.MODEL
        val androidVersion = android.os.Build.VERSION.SDK_INT

        return """
            تشخيص المؤشر:
            ================
            الجهاز: $deviceBrand $deviceModel
            أندرويد: $androidVersion
            صلاحية العرض: $overlayPermission
            حالة الإظهار: $isShown
            مرفق بالنافذة: $isAttached
            معاملات التخطيط: $hasLayoutParams
            الموقع: ${getCurrentPosition()}
        """.trimIndent()
    }
}

// Extension function for AnimatorSet
private fun AnimatorSet.doOnEnd(action: () -> Unit) {
    addListener(object : android.animation.Animator.AnimatorListener {
        override fun onAnimationStart(animation: android.animation.Animator) {}
        override fun onAnimationEnd(animation: android.animation.Animator) { action() }
        override fun onAnimationCancel(animation: android.animation.Animator) {}
        override fun onAnimationRepeat(animation: android.animation.Animator) {}
    })
}
