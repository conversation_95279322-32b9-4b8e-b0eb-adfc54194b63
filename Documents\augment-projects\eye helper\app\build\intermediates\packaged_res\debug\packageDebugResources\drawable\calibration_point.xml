<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Outer ring -->
    <item>
        <shape android:shape="oval">
            <solid android:color="@color/primary" />
            <size android:width="32dp" android:height="32dp" />
        </shape>
    </item>
    <!-- Inner circle -->
    <item android:top="8dp" android:bottom="8dp" android:left="8dp" android:right="8dp">
        <shape android:shape="oval">
            <solid android:color="@color/white" />
        </shape>
    </item>
    <!-- Center dot -->
    <item android:top="12dp" android:bottom="12dp" android:left="12dp" android:right="12dp">
        <shape android:shape="oval">
            <solid android:color="@color/primary" />
        </shape>
    </item>
</layer-list>
