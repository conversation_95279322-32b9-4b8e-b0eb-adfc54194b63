package com.motasem.visualpointer.service

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log

/**
 * BroadcastReceiver to restart the eye tracking service when it's killed
 */
class ServiceRestartReceiver : BroadcastReceiver() {

    companion object {
        private const val TAG = "ServiceRestartReceiver"
    }

    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "Received broadcast: ${intent.action}")

        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED -> {
                Log.d(TAG, "Device booted, checking if service should restart")
                restartServiceIfNeeded(context)
            }
            Intent.ACTION_MY_PACKAGE_REPLACED,
            Intent.ACTION_PACKAGE_REPLACED -> {
                Log.d(TAG, "Package replaced, restarting service")
                restartServiceIfNeeded(context)
            }
            "com.motasem.visualpointer.RESTART_SERVICE" -> {
                Log.d(TAG, "Manual service restart requested")
                restartService(context)
            }
        }
    }

    private fun restartServiceIfNeeded(context: Context) {
        // Check if the service was running before
        val prefs = context.getSharedPreferences("app_prefs", Context.MODE_PRIVATE)
        val wasServiceRunning = prefs.getBoolean("service_was_running", false)
        
        if (wasServiceRunning) {
            Log.d(TAG, "Service was running before, restarting...")
            restartService(context)
        }
    }

    private fun restartService(context: Context) {
        try {
            val serviceIntent = Intent(context, EyeTrackingService::class.java)
            serviceIntent.putExtra("RESTART_FROM_RECEIVER", true)
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent)
            } else {
                context.startService(serviceIntent)
            }
            
            Log.d(TAG, "Service restart initiated")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to restart service", e)
        }
    }
}
