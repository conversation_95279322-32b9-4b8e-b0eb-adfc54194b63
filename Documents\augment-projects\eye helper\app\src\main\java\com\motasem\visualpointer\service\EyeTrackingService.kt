package com.motasem.visualpointer.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.IBinder
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleService
import com.google.mlkit.vision.face.Face
import com.motasem.visualpointer.MainActivity
import com.motasem.visualpointer.R
import com.motasem.visualpointer.camera.BackgroundCameraManager
import com.motasem.visualpointer.databinding.OverlayCursorBinding
import com.motasem.visualpointer.gesture.GestureManager
import com.motasem.visualpointer.model.TrackingStatus
import com.motasem.visualpointer.tracking.BlinkDetector
import com.motasem.visualpointer.tracking.EyeTracker
import com.motasem.visualpointer.ui.FloatingCursor
import com.motasem.visualpointer.utils.PreferencesManager
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

/**
 * Foreground service for eye tracking and cursor overlay with background camera support
 */
class EyeTrackingService : LifecycleService() {

    companion object {
        private const val TAG = "EyeTrackingService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "eye_tracking_channel"
        private var instance: EyeTrackingService? = null

        fun getInstance(): EyeTrackingService? = instance
    }

    private lateinit var windowManager: WindowManager
    private lateinit var floatingCursor: FloatingCursor

    // Tracking components
    private lateinit var preferencesManager: PreferencesManager
    private lateinit var eyeTracker: EyeTracker
    private lateinit var blinkDetector: BlinkDetector
    private lateinit var gestureManager: GestureManager
    private var wakeLock: android.os.PowerManager.WakeLock? = null

    // Background camera operation
    private lateinit var cameraExecutor: ExecutorService
    private lateinit var backgroundCameraManager: BackgroundCameraManager
    private var isCameraActive = false

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "Service created")

        instance = this
        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager

        // Initialize components
        preferencesManager = PreferencesManager(this)
        eyeTracker = EyeTracker(preferencesManager)
        blinkDetector = BlinkDetector(preferencesManager)
        gestureManager = GestureManager(this)
        floatingCursor = FloatingCursor(this)

        // Setup blink detection
        blinkDetector.addBlinkListener { blinkEvent ->
            gestureManager.processBlink(blinkEvent)
        }

        // Setup gesture feedback
        gestureManager.addGestureListener { gestureType, position ->
            when (gestureType) {
                GestureManager.GestureType.CLICK -> floatingCursor.animateClick()
                else -> { /* Handle other gestures if needed */ }
            }
        }

        createNotificationChannel()

        // إنشاء WakeLock لمنع النوم
        val powerManager = getSystemService(POWER_SERVICE) as android.os.PowerManager
        wakeLock = powerManager.newWakeLock(
            android.os.PowerManager.PARTIAL_WAKE_LOCK,
            "VisualPointer::EyeTrackingWakeLock"
        )

        // Initialize camera executor
        cameraExecutor = Executors.newSingleThreadExecutor()

        // Initialize background camera manager
        backgroundCameraManager = BackgroundCameraManager(
            this,
            this,
            ::onBackgroundTrackingStatusChanged
        )
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "=== بدء خدمة تتبع الوجه ===")

        // حفظ حالة تشغيل الخدمة
        val prefs = getSharedPreferences("app_prefs", MODE_PRIVATE)
        prefs.edit().putBoolean("service_was_running", true).apply()

        // معالجة إجراءات الإشعار
        when (intent?.action) {
            "STOP_TRACKING" -> {
                Log.d(TAG, "إيقاف التتبع من الإشعار")
                prefs.edit().putBoolean("service_was_running", false).apply()
                stopSelf()
                return START_NOT_STICKY
            }
        }

        val notification = createNotification()
        startForeground(NOTIFICATION_ID, notification)

        // تفعيل WakeLock لمنع النوم
        try {
            wakeLock?.acquire()
            Log.d(TAG, "WakeLock acquired")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to acquire WakeLock", e)
        }

        val forceShow = intent?.getBooleanExtra("FORCE_SHOW_CURSOR", false) ?: false
        val restartFromReceiver = intent?.getBooleanExtra("RESTART_FROM_RECEIVER", false) ?: false

        Log.d(TAG, "Force show cursor: $forceShow, Restart from receiver: $restartFromReceiver")

        // محاولة إظهار المؤشر بطرق متعددة
        showCursorWithMultipleAttempts(forceShow)

        // Start background camera for continuous face tracking
        startBackgroundCameraTracking()

        // إرسال broadcast لإعلام النظام أن الخدمة نشطة
        sendBroadcast(Intent("com.motasem.visualpointer.SERVICE_STARTED"))

        return START_STICKY // إعادة تشغيل الخدمة إذا تم إيقافها
    }

    private fun showCursorWithMultipleAttempts(forceShow: Boolean) {
        Log.d(TAG, "محاولة إظهار المؤشر...")

        // المحاولة الأولى فوراً
        try {
            floatingCursor.show()
            Log.d(TAG, "المحاولة الأولى: تم")
        } catch (e: Exception) {
            Log.e(TAG, "المحاولة الأولى فشلت", e)
        }

        // المحاولة الثانية بعد ثانية
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            try {
                if (!floatingCursor.isVisible()) {
                    floatingCursor.show()
                    Log.d(TAG, "المحاولة الثانية: تم")
                }
            } catch (e: Exception) {
                Log.e(TAG, "المحاولة الثانية فشلت", e)
            }
        }, 1000)

        // المحاولة الثالثة بعد 3 ثوان
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            try {
                if (!floatingCursor.isVisible()) {
                    floatingCursor.show()
                    Log.d(TAG, "المحاولة الثالثة: تم")
                }
            } catch (e: Exception) {
                Log.e(TAG, "المحاولة الثالثة فشلت", e)
            }
        }, 3000)

        // إذا كان مطلوب إجبار الإظهار، محاولات إضافية
        if (forceShow) {
            for (i in 1..5) {
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    try {
                        if (!floatingCursor.isVisible()) {
                            floatingCursor.show()
                            Log.d(TAG, "محاولة إجبارية $i: تم")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "محاولة إجبارية $i فشلت", e)
                    }
                }, (i * 2000).toLong()) // كل ثانيتين
            }
        }
    }





    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    /**
     * Get the EyeTracker instance for external use
     */
    fun getEyeTracker(): EyeTracker {
        return eyeTracker
    }

    /**
     * Update cursor position from external source (like MainActivity camera)
     */
    fun updateCursorFromExternal(x: Float, y: Float) {
        updateCursorPosition(x, y)
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "Service destroyed")

        // حفظ حالة إيقاف الخدمة
        val prefs = getSharedPreferences("app_prefs", MODE_PRIVATE)
        prefs.edit().putBoolean("service_was_running", false).apply()

        // تحرير WakeLock
        try {
            wakeLock?.release()
            Log.d(TAG, "WakeLock released")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to release WakeLock", e)
        }

        // Cleanup camera executor
        try {
            if (::cameraExecutor.isInitialized) {
                cameraExecutor.shutdown()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error shutting down camera executor", e)
        }

        instance = null
        floatingCursor.hide()

        // Cleanup tracking components
        blinkDetector.clearListeners()
        gestureManager.clearListeners()
        eyeTracker.reset()

        // محاولة إعادة تشغيل الخدمة إذا تم إيقافها بشكل غير متوقع
        try {
            val restartIntent = Intent("com.motasem.visualpointer.RESTART_SERVICE")
            sendBroadcast(restartIntent)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send restart broadcast", e)
        }
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Eye Tracking Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Service for eye tracking and cursor control"
                setShowBadge(false)
            }

            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // إضافة إجراء لإيقاف التتبع
        val stopIntent = Intent(this, EyeTrackingService::class.java).apply {
            action = "STOP_TRACKING"
        }
        val stopPendingIntent = PendingIntent.getService(
            this, 1, stopIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("🎯 المؤشر البصري نشط")
            .setContentText("تتبع الوجه 3D يعمل - اضغط للعودة للتطبيق")
            .setSmallIcon(R.drawable.ic_eye)
            .setContentIntent(pendingIntent)
            .setOngoing(true) // منع إزالة الإشعار
            .setPriority(NotificationCompat.PRIORITY_HIGH) // أولوية عالية
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .setForegroundServiceBehavior(NotificationCompat.FOREGROUND_SERVICE_IMMEDIATE)
            .addAction(android.R.drawable.ic_menu_close_clear_cancel, "إيقاف", stopPendingIntent)
            .setAutoCancel(false) // منع الإلغاء التلقائي
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .setShowWhen(true)
            .setWhen(System.currentTimeMillis())
            .setUsesChronometer(true) // إظهار مؤقت
            .build()
    }

    /**
     * Update cursor position based on eye tracking data
     */
    fun updateCursorPosition(x: Float, y: Float) {
        floatingCursor.updatePosition(x, y, animate = true)
    }

    /**
     * Show cursor (make it visible)
     */
    fun showCursor() {
        floatingCursor.setVisible(true)
    }

    /**
     * Hide cursor (make it invisible)
     */
    fun hideCursor() {
        floatingCursor.setVisible(false)
    }

    /**
     * Process eye tracking data from camera
     */
    fun processEyeTrackingData(leftEyeProb: Float, rightEyeProb: Float, gazeX: Float, gazeY: Float) {
        // Update blink detector
        blinkDetector.processEyeStates(leftEyeProb, rightEyeProb)

        // Update cursor position
        updateCursorPosition(gazeX, gazeY)
        gestureManager.updateCursorPosition(gazeX, gazeY)
    }



    /**
     * Get blink detector instance
     */
    fun getBlinkDetector(): BlinkDetector = blinkDetector

    /**
     * Get gesture manager instance
     */
    fun getGestureManager(): GestureManager = gestureManager

    /**
     * Check if cursor is visible
     */
    fun isCursorVisible(): Boolean {
        return floatingCursor.isVisible()
    }

    /**
     * Force show cursor (for debugging)
     */
    fun forceShowCursor() {
        try {
            floatingCursor.show()
            Log.d(TAG, "Cursor force shown")
        } catch (e: Exception) {
            Log.e(TAG, "Error force showing cursor", e)
        }
    }
}
