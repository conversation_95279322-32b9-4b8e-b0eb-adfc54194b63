<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res"><file name="calibration_point" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\drawable\calibration_point.xml" qualifiers="" type="drawable"/><file name="context_menu_background" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\drawable\context_menu_background.xml" qualifiers="" type="drawable"/><file name="context_menu_item_background" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\drawable\context_menu_item_background.xml" qualifiers="" type="drawable"/><file name="cursor_circle" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\drawable\cursor_circle.xml" qualifiers="" type="drawable"/><file name="cursor_pulse_ring" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\drawable\cursor_pulse_ring.xml" qualifiers="" type="drawable"/><file name="ic_back" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\drawable\ic_back.xml" qualifiers="" type="drawable"/><file name="ic_click" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\drawable\ic_click.xml" qualifiers="" type="drawable"/><file name="ic_eye" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\drawable\ic_eye.xml" qualifiers="" type="drawable"/><file name="ic_home" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\drawable\ic_home.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_launcher_simple" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\drawable\ic_launcher_simple.xml" qualifiers="" type="drawable"/><file name="ic_long_click" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\drawable\ic_long_click.xml" qualifiers="" type="drawable"/><file name="ic_recent" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\drawable\ic_recent.xml" qualifiers="" type="drawable"/><file name="ic_scroll_down" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\drawable\ic_scroll_down.xml" qualifiers="" type="drawable"/><file name="ic_scroll_up" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\drawable\ic_scroll_up.xml" qualifiers="" type="drawable"/><file name="splash_background" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\drawable\splash_background.xml" qualifiers="" type="drawable"/><file name="status_background" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\drawable\status_background.xml" qualifiers="" type="drawable"/><file name="activity_calibration" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\layout\activity_calibration.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_settings" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\layout\activity_settings.xml" qualifiers="" type="layout"/><file name="context_menu_item" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\layout\context_menu_item.xml" qualifiers="" type="layout"/><file name="overlay_cursor" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\layout\overlay_cursor.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\mipmap-hdpi\ic_launcher.xml" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\mipmap-hdpi\ic_launcher_round.xml" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\mipmap-mdpi\ic_launcher.xml" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\mipmap-mdpi\ic_launcher_round.xml" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\mipmap-xhdpi\ic_launcher.xml" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\mipmap-xhdpi\ic_launcher_round.xml" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\mipmap-xxhdpi\ic_launcher.xml" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.xml" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\mipmap-xxxhdpi\ic_launcher.xml" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.xml" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\values\colors.xml" qualifiers=""><color name="primary">#2196F3</color><color name="primary_dark">#1976D2</color><color name="primary_light">#BBDEFB</color><color name="accent">#FF4081</color><color name="accent_dark">#C2185B</color><color name="background">#FAFAFA</color><color name="surface">#FFFFFF</color><color name="card_background">#FFFFFF</color><color name="text_primary">#212121</color><color name="text_secondary">#757575</color><color name="text_hint">#BDBDBD</color><color name="text_white">#FFFFFF</color><color name="success">#4CAF50</color><color name="warning">#FF9800</color><color name="error">#F44336</color><color name="info">#2196F3</color><color name="cursor_default">#FF4081</color><color name="cursor_active">#4CAF50</color><color name="cursor_inactive">#9E9E9E</color><color name="face_outline">#4CAF50</color><color name="eye_open">#4CAF50</color><color name="eye_closed">#FF5722</color><color name="overlay_background">#80000000</color><color name="semi_transparent">#80FFFFFF</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="transparent">#00000000</color></file><file path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#2196F3</color></file><file path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">المؤشر البصري</string><string name="app_name_en">Visual Pointer</string><string name="welcome_title">مرحباً بك في المؤشر البصري</string><string name="welcome_subtitle">تحكم في جهازك باستخدام عينيك</string><string name="start_tracking">بدء التتبع</string><string name="settings">الإعدادات</string><string name="calibration">المعايرة</string><string name="help">المساعدة</string><string name="camera_permission_title">صلاحية الكاميرا مطلوبة</string><string name="camera_permission_message">يحتاج التطبيق إلى صلاحية الكاميرا لتتبع حركة العين</string><string name="overlay_permission_title">صلاحية العرض مطلوبة</string><string name="overlay_permission_message">يحتاج التطبيق إلى صلاحية العرض فوق التطبيقات الأخرى لإظهار المؤشر</string><string name="accessibility_permission_title">خدمة إمكانية الوصول مطلوبة</string><string name="accessibility_permission_message">يحتاج التطبيق إلى خدمة إمكانية الوصول للتحكم في الجهاز</string><string name="grant_permission">منح الصلاحية</string><string name="cancel">إلغاء</string><string name="ok">موافق</string><string name="no_face_detected">لم يتم اكتشاف وجه</string><string name="multiple_faces_detected">تم اكتشاف عدة وجوه</string><string name="face_detected">تم اكتشاف الوجه</string><string name="left_eye_closed">العين اليسرى مغلقة</string><string name="right_eye_closed">العين اليمنى مغلقة</string><string name="both_eyes_closed">كلتا العينين مغلقتان</string><string name="tracking_active">التتبع نشط</string><string name="sensitivity_settings">إعدادات الحساسية</string><string name="eye_tracking_sensitivity">حساسية تتبع العين</string><string name="blink_detection_sensitivity">حساسية اكتشاف الغمز</string><string name="cursor_speed">سرعة المؤشر</string><string name="blink_duration">مدة الغمزة</string><string name="visual_settings">الإعدادات البصرية</string><string name="cursor_size">حجم المؤشر</string><string name="cursor_color">لون المؤشر</string><string name="show_face_outline">إظهار حدود الوجه</string><string name="calibration_title">معايرة النظام</string><string name="calibration_instruction">انظر إلى النقاط التي تظهر على الشاشة</string><string name="calibration_point">نقطة المعايرة %1$d من %2$d</string><string name="calibration_complete">تمت المعايرة بنجاح</string><string name="calibration_failed">فشلت المعايرة، يرجى المحاولة مرة أخرى</string><string name="accessibility_service_description">خدمة المؤشر البصري للتحكم في الجهاز عبر تتبع العين</string><string name="action_click">نقر</string><string name="action_long_click">نقر طويل</string><string name="action_back">رجوع</string><string name="action_home">الرئيسية</string><string name="action_recent">التطبيقات الحديثة</string><string name="action_scroll_up">تمرير لأعلى</string><string name="action_scroll_down">تمرير لأسفل</string><string name="instruction_right_blink">اغمز بالعين اليمنى للنقر</string><string name="instruction_left_blink">اغمز بالعين اليسرى للرجوع</string><string name="instruction_both_blink">اغمز بكلتا العينين لفتح القائمة</string><string name="error_camera_not_available">الكاميرا غير متاحة</string><string name="error_face_detection_failed">فشل في اكتشاف الوجه</string><string name="error_permission_denied">تم رفض الصلاحية</string><string name="error_service_not_running">الخدمة غير نشطة</string></file><file path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.VisualPointer" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        
        <item name="colorSecondary">@color/accent</item>
        <item name="colorSecondaryVariant">@color/accent_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        
        
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorOnSurface">@color/text_primary</item>
        
        
        <item name="android:statusBarColor">@color/primary_dark</item>
        
        
        <item name="android:windowContentTransitions">true</item>
    </style><style name="Theme.VisualPointer.Splash" parent="Theme.VisualPointer">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
    </style><style name="Theme.VisualPointer.Fullscreen" parent="Theme.VisualPointer">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style><style name="CardStyle">
        <item name="android:layout_margin">16dp</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:background">@color/card_background</item>
    </style><style name="PrimaryButton" parent="Widget.Material3.Button">
        <item name="android:layout_height">56dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="cornerRadius">28dp</item>
    </style><style name="SecondaryButton" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:layout_height">56dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:textSize">16sp</item>
        <item name="cornerRadius">28dp</item>
    </style><style name="TitleText">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:layout_margin">16dp</item>
    </style><style name="SubtitleText">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:layout_margin">8dp</item>
    </style><style name="StatusText">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:padding">8dp</item>
        <item name="android:background">@drawable/status_background</item>
        <item name="android:textColor">@color/white</item>
    </style></file><file name="accessibility_service_config" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\xml\accessibility_service_config.xml" qualifiers="" type="xml"/><file name="backup_rules" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\eye helper\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>