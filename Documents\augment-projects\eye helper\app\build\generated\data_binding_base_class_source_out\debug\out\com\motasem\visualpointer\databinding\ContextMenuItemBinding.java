// Generated by view binder compiler. Do not edit!
package com.motasem.visualpointer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.motasem.visualpointer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ContextMenuItemBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final View menuItemIcon;

  @NonNull
  public final TextView menuItemTitle;

  private ContextMenuItemBinding(@NonNull LinearLayout rootView, @NonNull View menuItemIcon,
      @NonNull TextView menuItemTitle) {
    this.rootView = rootView;
    this.menuItemIcon = menuItemIcon;
    this.menuItemTitle = menuItemTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ContextMenuItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ContextMenuItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.context_menu_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ContextMenuItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.menuItemIcon;
      View menuItemIcon = ViewBindings.findChildViewById(rootView, id);
      if (menuItemIcon == null) {
        break missingId;
      }

      id = R.id.menuItemTitle;
      TextView menuItemTitle = ViewBindings.findChildViewById(rootView, id);
      if (menuItemTitle == null) {
        break missingId;
      }

      return new ContextMenuItemBinding((LinearLayout) rootView, menuItemIcon, menuItemTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
