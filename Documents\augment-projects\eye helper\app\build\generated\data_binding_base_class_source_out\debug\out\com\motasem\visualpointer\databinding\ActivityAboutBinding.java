// Generated by view binder compiler. Do not edit!
package com.motasem.visualpointer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.motasem.visualpointer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAboutBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView appVersionText;

  @NonNull
  public final LinearLayout emailContactCard;

  @NonNull
  public final LinearLayout githubLinkCard;

  @NonNull
  public final LinearLayout privacyPolicyCard;

  @NonNull
  public final LinearLayout rateAppCard;

  @NonNull
  public final LinearLayout shareAppCard;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final LinearLayout whatsappContactCard;

  private ActivityAboutBinding(@NonNull LinearLayout rootView, @NonNull TextView appVersionText,
      @NonNull LinearLayout emailContactCard, @NonNull LinearLayout githubLinkCard,
      @NonNull LinearLayout privacyPolicyCard, @NonNull LinearLayout rateAppCard,
      @NonNull LinearLayout shareAppCard, @NonNull Toolbar toolbar,
      @NonNull LinearLayout whatsappContactCard) {
    this.rootView = rootView;
    this.appVersionText = appVersionText;
    this.emailContactCard = emailContactCard;
    this.githubLinkCard = githubLinkCard;
    this.privacyPolicyCard = privacyPolicyCard;
    this.rateAppCard = rateAppCard;
    this.shareAppCard = shareAppCard;
    this.toolbar = toolbar;
    this.whatsappContactCard = whatsappContactCard;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAboutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAboutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_about, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAboutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.appVersionText;
      TextView appVersionText = ViewBindings.findChildViewById(rootView, id);
      if (appVersionText == null) {
        break missingId;
      }

      id = R.id.emailContactCard;
      LinearLayout emailContactCard = ViewBindings.findChildViewById(rootView, id);
      if (emailContactCard == null) {
        break missingId;
      }

      id = R.id.githubLinkCard;
      LinearLayout githubLinkCard = ViewBindings.findChildViewById(rootView, id);
      if (githubLinkCard == null) {
        break missingId;
      }

      id = R.id.privacyPolicyCard;
      LinearLayout privacyPolicyCard = ViewBindings.findChildViewById(rootView, id);
      if (privacyPolicyCard == null) {
        break missingId;
      }

      id = R.id.rateAppCard;
      LinearLayout rateAppCard = ViewBindings.findChildViewById(rootView, id);
      if (rateAppCard == null) {
        break missingId;
      }

      id = R.id.shareAppCard;
      LinearLayout shareAppCard = ViewBindings.findChildViewById(rootView, id);
      if (shareAppCard == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.whatsappContactCard;
      LinearLayout whatsappContactCard = ViewBindings.findChildViewById(rootView, id);
      if (whatsappContactCard == null) {
        break missingId;
      }

      return new ActivityAboutBinding((LinearLayout) rootView, appVersionText, emailContactCard,
          githubLinkCard, privacyPolicyCard, rateAppCard, shareAppCard, toolbar,
          whatsappContactCard);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
