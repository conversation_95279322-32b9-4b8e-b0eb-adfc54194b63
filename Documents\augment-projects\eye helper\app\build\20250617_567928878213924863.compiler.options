"-Xallow-no-source-files" "-classpath" "C:\\Users\\<USER>\\Documents\\augment-projects\\eye helper\\app\\build\\intermediates\\compile_and_runtime_not_namespaced_r_class_jar\\debug\\processDebugResources\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f926d05bf968e914a8348b8f1012331d\\transformed\\viewbinding-8.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e5062477e31d533eed3646219182bedc\\transformed\\material-1.11.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\11a2d066a7f101c28fc66217afb24d6d\\transformed\\appcompat-1.6.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b38f9c4393268a34c9bc216457488b89\\transformed\\viewpager2-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d672b798aa16dda061b0f24a3ce09e4b\\transformed\\play-services-mlkit-face-detection-17.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\62335c208108d1abad38a7dd51168f77\\transformed\\vision-common-17.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0436ecc88cbae58ef3dc3698dd71d212\\transformed\\common-18.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\48ae73132a4c715ded82d06de4d2f30f\\transformed\\play-services-base-18.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\60068c5af5cc949fc5c0458cc08bc4db\\transformed\\vision-interfaces-16.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a9a70f8822b0e0b98b2566d8dab18cb2\\transformed\\play-services-tasks-18.0.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4111d4197e4ccc5f1182fe768a315994\\transformed\\play-services-basement-18.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9d3b77205f7ae2e40f8f2664904c9a52\\transformed\\fragment-1.3.6-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0535ad8517304d28d017bf05d219594e\\transformed\\activity-1.8.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\86a37701319588f714b71ccde5663cdc\\transformed\\activity-ktx-1.8.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2e739314d5c3828a90a48c02658f5116\\transformed\\camera-video-1.3.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0867a2062e85fe56fc331b23b5c5f956\\transformed\\camera-view-1.3.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b2649a310b8138a54d549fc75f4de6cb\\transformed\\camera-lifecycle-1.3.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ef7ca54b135ae32fe2891b166c8c5179\\transformed\\camera-camera2-1.3.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1fe747b744f0fa4668173a5bb14c9b86\\transformed\\camera-core-1.3.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1562b3d293043de93ba26187d8696c99\\transformed\\lifecycle-livedata-core-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ddb9115767bb2cec4e7d7adb1ef10ac3\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b3f21bbb8c59bec07ad5bd761c6bebb3\\transformed\\dynamicanimation-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\eedbdafbb1b8031038b74a6b99e1bbb8\\transformed\\legacy-support-core-utils-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\276f66dae15002c6f2ec7b4d1724283b\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7ae1baa5d643ae45a576e620efda56de\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\98fe032419a77d899c7a4ddf1b3e02eb\\transformed\\lifecycle-livedata-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\55b664d329e6f290be0084a6a97d94ce\\transformed\\lifecycle-viewmodel-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1e137ee5e2a03a20828a9d24e8a04f12\\transformed\\lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3fccfbb929557d809d418a1e8a0921c5\\transformed\\core-ktx-1.12.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\635eab4a908c4f4838a3ca6da4d0d13c\\transformed\\appcompat-resources-1.6.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b45067fe7beeb9043d8fbfd6e6a22df\\transformed\\drawerlayout-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\188922e1a90657bfd699705ab6090b85\\transformed\\coordinatorlayout-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0e6102891c3ee9ec841958da0ea4d121\\transformed\\recyclerview-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ea2e930b7d567b5f144b95099acc6b63\\transformed\\transition-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\335f98c56116fafb479013983b35827b\\transformed\\vectordrawable-animated-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cf5f714bc069576d36c9ae3e7e1f3dd3\\transformed\\vectordrawable-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32969aee43f928c7e46c62b50e2f9db8\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adcc1e04a2ca98f02ce02dce3ab15cc7\\transformed\\customview-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f50b11a0cfe94424d08c7482d839fc26\\transformed\\core-1.12.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\edc34dd2974c7f82e7f4877beb0df05a\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dfe3a1202fec803ab1b5ed089aa2d517\\transformed\\lifecycle-runtime-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-coroutines-core-jvm\\1.7.1\\63a0779cf668e2a47d13fda7c3b0c4f8dc7762f4\\kotlinx-coroutines-core-jvm-1.7.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-coroutines-android\\1.7.1\\c2d86b569f10b7fc7e28d3f50c0eed97897d77a7\\kotlinx-coroutines-android-1.7.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib-jdk8\\1.9.10\\c7510d64a83411a649c76f2778304ddf71d7437b\\kotlin-stdlib-jdk8-1.9.10.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a160af355e86a092c7ddaad5d28dec89\\transformed\\constraintlayout-2.1.4-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8f72cf7f03096d8f8130e811601925b7\\transformed\\cursoradapter-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a2499c4ad3a9eda458f396b0ea93a5f1\\transformed\\savedstate-ktx-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f348b45f7aec92f1edfe8ff62c687429\\transformed\\savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\86f2b2a7e999aa02793111c9890715a2\\transformed\\cardview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\55592af138f09704a5a2fca03f410e95\\transformed\\transport-backend-cct-2.3.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1686eae34d589baf09fabf791a3b06c6\\transformed\\transport-runtime-2.2.6-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4d33d496f703adab5cf49a0ebdb2de1f\\transformed\\transport-api-2.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1db6002b8aa82999ad83ae946985d6d7\\transformed\\firebase-components-16.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\581d583a80b2693d5bef0c8b180190d1\\transformed\\firebase-encoders-json-17.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.firebase\\firebase-encoders\\16.1.0\\267565db8531da1483692e27eb58f93ec894c78\\firebase-encoders-16.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\94df921fbc71c19404a887a2b36c4be3\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cfc4632a1d54e97171434f21b8024c96\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4db6ead80f25a247c5df4099c0227bb6\\transformed\\exifinterface-1.3.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9d4416ce7c16f24701d964511e67f898\\transformed\\interpolator-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6864f4dbb5d96d533bd6d6ae53fdf8ea\\transformed\\documentfile-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bc3aff72596dea64aa1d61d2c963b575\\transformed\\localbroadcastmanager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\55f3bce4d2c51bf8d4748dcb687ddb26\\transformed\\print-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.annotation\\annotation-jvm\\1.6.0\\a7257339a052df0f91433cf9651231bbb802b502\\annotation-jvm-1.6.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib-jdk7\\1.9.10\\bc5bfc2690338defd5195b05c57562f2194eeb10\\kotlin-stdlib-jdk7-1.9.10.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4ce6f8f9fcfed1280859cdca2fdc8288\\transformed\\annotation-experimental-1.3.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib\\1.9.10\\72812e8a368917ab5c0a5081b56915ffdfec93b7\\kotlin-stdlib-1.9.10.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib-common\\1.9.10\\dafaf2c27f27c09220cee312df10917d9a5d97ce\\kotlin-stdlib-common-1.9.10.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains\\annotations\\23.0.0\\8cc20c07506ec18e0834947b84a864bfc094484e\\annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.guava\\listenablefuture\\1.0\\c949a840a6acbc5268d088e47b04177bf90b3cad\\listenablefuture-1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.resourceinspection\\resourceinspection-annotation\\1.0.1\\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\\resourceinspection-annotation-1.0.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\javax.inject\\javax.inject\\1\\6975da39a7040257bd51d21a231b76c915872d38\\javax.inject-1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9fd085b55f08dd5310dc60723473548a\\transformed\\image-1.0.0-beta1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.firebase\\firebase-annotations\\16.0.0\\dbeae20d6c97b747b59ef47b6dcf770ba1a60fa6\\firebase-annotations-16.0.0.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platforms\\android-34\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\build-tools\\35.0.0\\core-lambda-stubs.jar" "-d" "C:\\Users\\<USER>\\Documents\\augment-projects\\eye helper\\app\\build\\tmp\\kotlin-classes\\debug" "-jvm-target" "1.8" "-module-name" "app_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "C:\\Users\\<USER>\\Documents\\augment-projects\\eye helper\\app\\build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\motasem\\visualpointer\\databinding\\ActivityCalibrationBinding.java" "C:\\Users\\<USER>\\Documents\\augment-projects\\eye helper\\app\\build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\motasem\\visualpointer\\databinding\\ActivityMainBinding.java" "C:\\Users\\<USER>\\Documents\\augment-projects\\eye helper\\app\\build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\motasem\\visualpointer\\databinding\\ActivitySettingsBinding.java" "C:\\Users\\<USER>\\Documents\\augment-projects\\eye helper\\app\\build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\motasem\\visualpointer\\databinding\\ContextMenuItemBinding.java" "C:\\Users\\<USER>\\Documents\\augment-projects\\eye helper\\app\\build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\motasem\\visualpointer\\databinding\\OverlayCursorBinding.java" "C:\\Users\\<USER>\\Documents\\augment-projects\\eye helper\\app\\src\\main\\java\\com\\motasem\\visualpointer\\camera\\Analyzer.kt" "C:\\Users\\<USER>\\Documents\\augment-projects\\eye helper\\app\\src\\main\\java\\com\\motasem\\visualpointer\\camera\\CameraManager.kt" "C:\\Users\\<USER>\\Documents\\augment-projects\\eye helper\\app\\src\\main\\java\\com\\motasem\\visualpointer\\camera\\FaceDetectionAnalyzer.kt" "C:\\Users\\<USER>\\Documents\\augment-projects\\eye helper\\app\\src\\main\\java\\com\\motasem\\visualpointer\\gesture\\GestureManager.kt" "C:\\Users\\<USER>\\Documents\\augment-projects\\eye helper\\app\\src\\main\\java\\com\\motasem\\visualpointer\\MainActivity.kt" "C:\\Users\\<USER>\\Documents\\augment-projects\\eye helper\\app\\src\\main\\java\\com\\motasem\\visualpointer\\model\\TrackingStatus.kt" "C:\\Users\\<USER>\\Documents\\augment-projects\\eye helper\\app\\src\\main\\java\\com\\motasem\\visualpointer\\service\\EyeTrackingService.kt" "C:\\Users\\<USER>\\Documents\\augment-projects\\eye helper\\app\\src\\main\\java\\com\\motasem\\visualpointer\\service\\VisualPointerAccessibilityService.kt" "C:\\Users\\<USER>\\Documents\\augment-projects\\eye helper\\app\\src\\main\\java\\com\\motasem\\visualpointer\\tracking\\BlinkDetector.kt" "C:\\Users\\<USER>\\Documents\\augment-projects\\eye helper\\app\\src\\main\\java\\com\\motasem\\visualpointer\\tracking\\EyeTracker.kt" "C:\\Users\\<USER>\\Documents\\augment-projects\\eye helper\\app\\src\\main\\java\\com\\motasem\\visualpointer\\ui\\CalibrationActivity.kt" "C:\\Users\\<USER>\\Documents\\augment-projects\\eye helper\\app\\src\\main\\java\\com\\motasem\\visualpointer\\ui\\ContextMenu.kt" "C:\\Users\\<USER>\\Documents\\augment-projects\\eye helper\\app\\src\\main\\java\\com\\motasem\\visualpointer\\ui\\FaceGraphic.kt" "C:\\Users\\<USER>\\Documents\\augment-projects\\eye helper\\app\\src\\main\\java\\com\\motasem\\visualpointer\\ui\\FloatingCursor.kt" "C:\\Users\\<USER>\\Documents\\augment-projects\\eye helper\\app\\src\\main\\java\\com\\motasem\\visualpointer\\ui\\GraphicOverlay.kt" "C:\\Users\\<USER>\\Documents\\augment-projects\\eye helper\\app\\src\\main\\java\\com\\motasem\\visualpointer\\ui\\SettingsActivity.kt" "C:\\Users\\<USER>\\Documents\\augment-projects\\eye helper\\app\\src\\main\\java\\com\\motasem\\visualpointer\\utils\\AnalyticsManager.kt" "C:\\Users\\<USER>\\Documents\\augment-projects\\eye helper\\app\\src\\main\\java\\com\\motasem\\visualpointer\\utils\\PermissionHelper.kt" "C:\\Users\\<USER>\\Documents\\augment-projects\\eye helper\\app\\src\\main\\java\\com\\motasem\\visualpointer\\utils\\PreferencesManager.kt"