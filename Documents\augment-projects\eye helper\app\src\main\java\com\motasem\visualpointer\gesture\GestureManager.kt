package com.motasem.visualpointer.gesture

import android.graphics.PointF
import android.util.Log
import com.motasem.visualpointer.model.BlinkEvent
import com.motasem.visualpointer.model.BlinkEye
import com.motasem.visualpointer.service.VisualPointerAccessibilityService
import com.motasem.visualpointer.ui.ContextMenu

/**
 * Manages gesture recognition and command execution
 */
class GestureManager(private val context: android.content.Context? = null) {

    companion object {
        private const val TAG = "GestureManager"
        private const val LONG_PRESS_DURATION = 1000L
        private const val SWIPE_THRESHOLD = 100f
    }

    private var currentCursorPosition = PointF(0f, 0f)
    private var lastClickTime = 0L
    private var isLongPressActive = false
    private var contextMenu: ContextMenu? = null

    private val gestureListeners = mutableListOf<(GestureType, PointF?) -> Unit>()

    init {
        context?.let {
            contextMenu = ContextMenu(it).apply {
                setOnMenuItemClickListener { gestureType ->
                    executeGesture(gestureType, currentCursorPosition)
                }
            }
        }
    }

    enum class GestureType {
        CLICK,
        LONG_CLICK,
        BACK,
        HOME,
        RECENT_APPS,
        SCROLL_UP,
        SCROLL_DOWN,
        SWIPE_LEFT,
        SWIPE_RIGHT,
        MENU
    }

    /**
     * Process blink events and convert to gestures
     */
    fun processBlink(blinkEvent: BlinkEvent) {
        Log.d(TAG, "Processing blink: ${blinkEvent.eye}, duration: ${blinkEvent.duration}ms")
        
        when (blinkEvent.eye) {
            BlinkEye.RIGHT -> {
                handleRightEyeBlink(blinkEvent)
            }
            BlinkEye.LEFT -> {
                handleLeftEyeBlink(blinkEvent)
            }
            BlinkEye.BOTH -> {
                handleBothEyesBlink(blinkEvent)
            }
        }
    }

    /**
     * Handle right eye blink (click gesture)
     */
    private fun handleRightEyeBlink(blinkEvent: BlinkEvent) {
        val currentTime = System.currentTimeMillis()
        
        when {
            blinkEvent.duration > LONG_PRESS_DURATION -> {
                // Long blink = long press
                executeGesture(GestureType.LONG_CLICK, currentCursorPosition)
            }
            currentTime - lastClickTime < 500L -> {
                // Double click (if needed in future)
                executeGesture(GestureType.CLICK, currentCursorPosition)
            }
            else -> {
                // Single click
                executeGesture(GestureType.CLICK, currentCursorPosition)
                lastClickTime = currentTime
            }
        }
    }

    /**
     * Handle left eye blink (back gesture)
     */
    private fun handleLeftEyeBlink(blinkEvent: BlinkEvent) {
        when {
            blinkEvent.duration > LONG_PRESS_DURATION -> {
                // Long left blink = home
                executeGesture(GestureType.HOME, null)
            }
            else -> {
                // Short left blink = back
                executeGesture(GestureType.BACK, null)
            }
        }
    }

    /**
     * Handle both eyes blink (menu/special actions)
     */
    private fun handleBothEyesBlink(blinkEvent: BlinkEvent) {
        when {
            blinkEvent.duration > LONG_PRESS_DURATION -> {
                // Long both eyes blink = recent apps
                executeGesture(GestureType.RECENT_APPS, null)
            }
            else -> {
                // Short both eyes blink = menu
                executeGesture(GestureType.MENU, null)
            }
        }
    }

    /**
     * Execute the specified gesture
     */
    private fun executeGesture(gestureType: GestureType, position: PointF?) {
        Log.d(TAG, "Executing gesture: $gestureType at position: $position")
        
        val accessibilityService = VisualPointerAccessibilityService.getInstance()
        
        if (accessibilityService == null) {
            Log.w(TAG, "Accessibility service not available")
            notifyGestureListeners(gestureType, position)
            return
        }

        when (gestureType) {
            GestureType.CLICK -> {
                position?.let { pos ->
                    accessibilityService.performClick(pos.x, pos.y)
                }
            }
            
            GestureType.LONG_CLICK -> {
                position?.let { pos ->
                    accessibilityService.performLongClick(pos.x, pos.y)
                }
            }
            
            GestureType.BACK -> {
                accessibilityService.performBack()
            }
            
            GestureType.HOME -> {
                accessibilityService.performHome()
            }
            
            GestureType.RECENT_APPS -> {
                accessibilityService.performRecents()
            }
            
            GestureType.SCROLL_UP -> {
                accessibilityService.scrollUp()
            }
            
            GestureType.SCROLL_DOWN -> {
                accessibilityService.scrollDown()
            }
            
            GestureType.SWIPE_LEFT -> {
                position?.let { pos ->
                    accessibilityService.performSwipe(
                        pos.x, pos.y,
                        pos.x - SWIPE_THRESHOLD, pos.y
                    )
                }
            }
            
            GestureType.SWIPE_RIGHT -> {
                position?.let { pos ->
                    accessibilityService.performSwipe(
                        pos.x, pos.y,
                        pos.x + SWIPE_THRESHOLD, pos.y
                    )
                }
            }
            
            GestureType.MENU -> {
                // Show custom menu (to be implemented)
                showContextMenu(position)
            }
        }
        
        // Notify listeners
        notifyGestureListeners(gestureType, position)
    }

    /**
     * Show context menu for advanced actions
     */
    private fun showContextMenu(position: PointF?) {
        contextMenu?.let { menu ->
            val x = position?.x ?: 0f
            val y = position?.y ?: 0f
            menu.show(x, y)
            Log.d(TAG, "Showing context menu at: ($x, $y)")
        }
    }

    /**
     * Update current cursor position
     */
    fun updateCursorPosition(x: Float, y: Float) {
        currentCursorPosition.set(x, y)
    }

    /**
     * Add gesture listener
     */
    fun addGestureListener(listener: (GestureType, PointF?) -> Unit) {
        gestureListeners.add(listener)
    }

    /**
     * Remove gesture listener
     */
    fun removeGestureListener(listener: (GestureType, PointF?) -> Unit) {
        gestureListeners.remove(listener)
    }

    /**
     * Notify all gesture listeners
     */
    private fun notifyGestureListeners(gestureType: GestureType, position: PointF?) {
        gestureListeners.forEach { listener ->
            try {
                listener(gestureType, position)
            } catch (e: Exception) {
                Log.e(TAG, "Error in gesture listener", e)
            }
        }
    }

    /**
     * Clear all listeners
     */
    fun clearListeners() {
        gestureListeners.clear()
    }

    /**
     * Reset gesture manager state
     */
    fun reset() {
        currentCursorPosition.set(0f, 0f)
        lastClickTime = 0L
        isLongPressActive = false
    }

    /**
     * Check if accessibility service is available
     */
    fun isAccessibilityServiceAvailable(): Boolean {
        return VisualPointerAccessibilityService.getInstance() != null
    }
}
