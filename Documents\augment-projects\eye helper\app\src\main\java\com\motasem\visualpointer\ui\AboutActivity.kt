package com.motasem.visualpointer.ui

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.motasem.visualpointer.R
import com.motasem.visualpointer.databinding.ActivityAboutBinding

/**
 * About page showing app information and developer contact
 */
class AboutActivity : AppCompatActivity() {

    private lateinit var binding: ActivityAboutBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAboutBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupUI()
        setupClickListeners()
    }

    private fun setupUI() {
        // Setup toolbar
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
            title = "حول البرنامج"
        }

        // Set app version
        try {
            val packageInfo = packageManager.getPackageInfo(packageName, 0)
            binding.appVersionText.text = "الإصدار: ${packageInfo.versionName}"
        } catch (e: Exception) {
            binding.appVersionText.text = "الإصدار: 1.0"
        }
    }

    private fun setupClickListeners() {
        // WhatsApp contact
        binding.whatsappContactCard.setOnClickListener {
            openWhatsApp()
        }

        // Email contact
        binding.emailContactCard.setOnClickListener {
            openEmail()
        }

        // GitHub link
        binding.githubLinkCard.setOnClickListener {
            openGitHub()
        }

        // Rate app
        binding.rateAppCard.setOnClickListener {
            rateApp()
        }

        // Share app
        binding.shareAppCard.setOnClickListener {
            shareApp()
        }

        // Privacy policy
        binding.privacyPolicyCard.setOnClickListener {
            showPrivacyPolicy()
        }
    }

    private fun openWhatsApp() {
        try {
            val intent = Intent(Intent.ACTION_VIEW)
            intent.data = Uri.parse("https://wa.me/201062606098?text=مرحباً، أحتاج مساعدة في تطبيق المؤشر البصري")
            startActivity(intent)
        } catch (e: Exception) {
            // Fallback: copy number to clipboard
            val clipboard = getSystemService(CLIPBOARD_SERVICE) as android.content.ClipboardManager
            val clip = android.content.ClipData.newPlainText("رقم المطور", "01062606098")
            clipboard.setPrimaryClip(clip)
            Toast.makeText(this, "تم نسخ رقم الواتساب: 01062606098", Toast.LENGTH_LONG).show()
        }
    }

    private fun openEmail() {
        try {
            val intent = Intent(Intent.ACTION_SENDTO).apply {
                data = Uri.parse("mailto:<EMAIL>")
                putExtra(Intent.EXTRA_SUBJECT, "استفسار حول تطبيق المؤشر البصري")
                putExtra(Intent.EXTRA_TEXT, "مرحباً،\n\nأحتاج مساعدة في تطبيق المؤشر البصري.\n\nشكراً")
            }
            startActivity(intent)
        } catch (e: Exception) {
            // Fallback: copy email to clipboard
            val clipboard = getSystemService(CLIPBOARD_SERVICE) as android.content.ClipboardManager
            val clip = android.content.ClipData.newPlainText("إيميل المطور", "<EMAIL>")
            clipboard.setPrimaryClip(clip)
            Toast.makeText(this, "تم نسخ الإيميل: <EMAIL>", Toast.LENGTH_LONG).show()
        }
    }

    private fun openGitHub() {
        try {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse("https://github.com/motasem-dev"))
            startActivity(intent)
        } catch (e: Exception) {
            Toast.makeText(this, "لا يمكن فتح الرابط", Toast.LENGTH_SHORT).show()
        }
    }

    private fun rateApp() {
        try {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=$packageName"))
            startActivity(intent)
        } catch (e: Exception) {
            try {
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id=$packageName"))
                startActivity(intent)
            } catch (e2: Exception) {
                Toast.makeText(this, "لا يمكن فتح متجر التطبيقات", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun shareApp() {
        val shareText = """
            🎯 المؤشر البصري - تطبيق رائع للتحكم بالجهاز بدون لمس!
            
            ✨ ميزات مذهلة:
            • تتبع ثلاثي الأبعاد للرأس
            • تحكم بالنظر فقط
            • مناسب لذوي الاحتياجات الخاصة
            • سهل الاستخدام
            
            📱 حمل التطبيق الآن:
            https://play.google.com/store/apps/details?id=$packageName
            
            👨‍💻 المطور: معتصم
            📞 واتساب: 01062606098
        """.trimIndent()

        val intent = Intent(Intent.ACTION_SEND).apply {
            type = "text/plain"
            putExtra(Intent.EXTRA_TEXT, shareText)
            putExtra(Intent.EXTRA_SUBJECT, "تطبيق المؤشر البصري")
        }
        startActivity(Intent.createChooser(intent, "مشاركة التطبيق"))
    }

    private fun showPrivacyPolicy() {
        val privacyText = """
            سياسة الخصوصية - المؤشر البصري
            
            🔒 نحن نحترم خصوصيتك:
            
            📷 الكاميرا:
            • تُستخدم فقط لتتبع الوجه محلياً
            • لا يتم حفظ أو إرسال أي صور
            • المعالجة تتم على الجهاز فقط
            
            📊 البيانات:
            • لا نجمع أي بيانات شخصية
            • لا نرسل معلومات للخوادم
            • جميع الإعدادات محلية
            
            🛡️ الأمان:
            • التطبيق يعمل بدون إنترنت
            • لا يصل لملفاتك الشخصية
            • آمن 100% للاستخدام
            
            📞 للاستفسارات:
            واتساب: 01062606098
        """.trimIndent()

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("🔒 سياسة الخصوصية")
            .setMessage(privacyText)
            .setPositiveButton("موافق") { dialog, _ -> dialog.dismiss() }
            .show()
    }

    override fun onSupportNavigateUp(): Boolean {
        finish()
        return true
    }
}
