// Generated by view binder compiler. Do not edit!
package com.motasem.visualpointer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.camera.view.PreviewView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.motasem.visualpointer.R;
import com.motasem.visualpointer.ui.GraphicOverlay;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button aboutButton;

  @NonNull
  public final LinearLayout buttonContainer;

  @NonNull
  public final Button calibrationButton;

  @NonNull
  public final FrameLayout cameraContainer;

  @NonNull
  public final GraphicOverlay graphicOverlay;

  @NonNull
  public final Button helpButton;

  @NonNull
  public final LinearLayout instructionsOverlay;

  @NonNull
  public final PreviewView previewView;

  @NonNull
  public final Button settingsButton;

  @NonNull
  public final Button startTrackingButton;

  @NonNull
  public final LinearLayout statusContainer;

  @NonNull
  public final TextView statusText;

  @NonNull
  public final TextView subtitleText;

  @NonNull
  public final LinearLayout titleContainer;

  @NonNull
  public final TextView titleText;

  private ActivityMainBinding(@NonNull ConstraintLayout rootView, @NonNull Button aboutButton,
      @NonNull LinearLayout buttonContainer, @NonNull Button calibrationButton,
      @NonNull FrameLayout cameraContainer, @NonNull GraphicOverlay graphicOverlay,
      @NonNull Button helpButton, @NonNull LinearLayout instructionsOverlay,
      @NonNull PreviewView previewView, @NonNull Button settingsButton,
      @NonNull Button startTrackingButton, @NonNull LinearLayout statusContainer,
      @NonNull TextView statusText, @NonNull TextView subtitleText,
      @NonNull LinearLayout titleContainer, @NonNull TextView titleText) {
    this.rootView = rootView;
    this.aboutButton = aboutButton;
    this.buttonContainer = buttonContainer;
    this.calibrationButton = calibrationButton;
    this.cameraContainer = cameraContainer;
    this.graphicOverlay = graphicOverlay;
    this.helpButton = helpButton;
    this.instructionsOverlay = instructionsOverlay;
    this.previewView = previewView;
    this.settingsButton = settingsButton;
    this.startTrackingButton = startTrackingButton;
    this.statusContainer = statusContainer;
    this.statusText = statusText;
    this.subtitleText = subtitleText;
    this.titleContainer = titleContainer;
    this.titleText = titleText;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.aboutButton;
      Button aboutButton = ViewBindings.findChildViewById(rootView, id);
      if (aboutButton == null) {
        break missingId;
      }

      id = R.id.buttonContainer;
      LinearLayout buttonContainer = ViewBindings.findChildViewById(rootView, id);
      if (buttonContainer == null) {
        break missingId;
      }

      id = R.id.calibrationButton;
      Button calibrationButton = ViewBindings.findChildViewById(rootView, id);
      if (calibrationButton == null) {
        break missingId;
      }

      id = R.id.cameraContainer;
      FrameLayout cameraContainer = ViewBindings.findChildViewById(rootView, id);
      if (cameraContainer == null) {
        break missingId;
      }

      id = R.id.graphicOverlay;
      GraphicOverlay graphicOverlay = ViewBindings.findChildViewById(rootView, id);
      if (graphicOverlay == null) {
        break missingId;
      }

      id = R.id.helpButton;
      Button helpButton = ViewBindings.findChildViewById(rootView, id);
      if (helpButton == null) {
        break missingId;
      }

      id = R.id.instructionsOverlay;
      LinearLayout instructionsOverlay = ViewBindings.findChildViewById(rootView, id);
      if (instructionsOverlay == null) {
        break missingId;
      }

      id = R.id.previewView;
      PreviewView previewView = ViewBindings.findChildViewById(rootView, id);
      if (previewView == null) {
        break missingId;
      }

      id = R.id.settingsButton;
      Button settingsButton = ViewBindings.findChildViewById(rootView, id);
      if (settingsButton == null) {
        break missingId;
      }

      id = R.id.startTrackingButton;
      Button startTrackingButton = ViewBindings.findChildViewById(rootView, id);
      if (startTrackingButton == null) {
        break missingId;
      }

      id = R.id.statusContainer;
      LinearLayout statusContainer = ViewBindings.findChildViewById(rootView, id);
      if (statusContainer == null) {
        break missingId;
      }

      id = R.id.statusText;
      TextView statusText = ViewBindings.findChildViewById(rootView, id);
      if (statusText == null) {
        break missingId;
      }

      id = R.id.subtitleText;
      TextView subtitleText = ViewBindings.findChildViewById(rootView, id);
      if (subtitleText == null) {
        break missingId;
      }

      id = R.id.titleContainer;
      LinearLayout titleContainer = ViewBindings.findChildViewById(rootView, id);
      if (titleContainer == null) {
        break missingId;
      }

      id = R.id.titleText;
      TextView titleText = ViewBindings.findChildViewById(rootView, id);
      if (titleText == null) {
        break missingId;
      }

      return new ActivityMainBinding((ConstraintLayout) rootView, aboutButton, buttonContainer,
          calibrationButton, cameraContainer, graphicOverlay, helpButton, instructionsOverlay,
          previewView, settingsButton, startTrackingButton, statusContainer, statusText,
          subtitleText, titleContainer, titleText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
