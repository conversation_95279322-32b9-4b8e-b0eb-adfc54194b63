<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <!-- Camera Preview -->
    <androidx.camera.view.PreviewView
        android:id="@+id/calibrationPreviewView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:scaleType="fillCenter" />

    <!-- Graphics Overlay -->
    <com.motasem.visualpointer.ui.GraphicOverlay
        android:id="@+id/calibrationGraphicOverlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <!-- Calibration Points Container -->
    <FrameLayout
        android:id="@+id/calibrationPointsContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- Calibration Point -->
        <View
            android:id="@+id/calibrationPoint"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center"
            android:background="@drawable/calibration_point"
            android:visibility="gone" />

    </FrameLayout>

    <!-- Instructions Overlay -->
    <LinearLayout
        android:id="@+id/instructionsContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="top"
        android:layout_margin="16dp"
        android:background="@drawable/status_background"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/calibrationTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/calibration_title"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/calibrationInstruction"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="@string/calibration_instruction"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/calibrationProgress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text=""
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="12sp" />

    </LinearLayout>

    <!-- Control Buttons -->
    <LinearLayout
        android:id="@+id/controlButtonsContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_margin="16dp"
        android:orientation="horizontal">

        <Button
            android:id="@+id/startCalibrationButton"
            style="@style/PrimaryButton"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="بدء المعايرة" />

        <Button
            android:id="@+id/skipCalibrationButton"
            style="@style/SecondaryButton"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="تخطي" />

        <Button
            android:id="@+id/backFromCalibrationButton"
            style="@style/SecondaryButton"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="رجوع" />

    </LinearLayout>

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/calibrationProgressBar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:layout_gravity="bottom"
        android:layout_marginBottom="80dp"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        android:max="9"
        android:progress="0"
        android:progressTint="@color/primary"
        android:visibility="gone" />

</FrameLayout>
