package com.motasem.visualpointer.ui

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.motasem.visualpointer.R
import com.motasem.visualpointer.gesture.GestureManager

/**
 * Floating context menu for advanced actions
 */
class ContextMenu(private val context: Context) {

    companion object {
        private const val TAG = "ContextMenu"
        private const val ANIMATION_DURATION = 300L
        private const val AUTO_HIDE_DELAY = 5000L // 5 seconds
    }

    private val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    private var menuView: LinearLayout? = null
    private var layoutParams: WindowManager.LayoutParams? = null
    private var isShown = false
    private var autoHideRunnable: Runnable? = null
    
    private val menuItems = listOf(
        MenuItem("نقر", GestureManager.GestureType.CLICK, R.drawable.ic_click),
        MenuItem("نقر طويل", GestureManager.GestureType.LONG_CLICK, R.drawable.ic_long_click),
        MenuItem("رجوع", GestureManager.GestureType.BACK, R.drawable.ic_back),
        MenuItem("الرئيسية", GestureManager.GestureType.HOME, R.drawable.ic_home),
        MenuItem("التطبيقات الحديثة", GestureManager.GestureType.RECENT_APPS, R.drawable.ic_recent),
        MenuItem("تمرير لأعلى", GestureManager.GestureType.SCROLL_UP, R.drawable.ic_scroll_up),
        MenuItem("تمرير لأسفل", GestureManager.GestureType.SCROLL_DOWN, R.drawable.ic_scroll_down)
    )

    private var onMenuItemClickListener: ((GestureManager.GestureType) -> Unit)? = null

    data class MenuItem(
        val title: String,
        val action: GestureManager.GestureType,
        val iconRes: Int
    )

    init {
        createMenuView()
        createLayoutParams()
    }

    private fun createMenuView() {
        menuView = LinearLayout(context).apply {
            orientation = LinearLayout.VERTICAL
            background = ContextCompat.getDrawable(context, R.drawable.context_menu_background)
            setPadding(16, 16, 16, 16)
        }

        // Add menu items
        menuItems.forEach { item ->
            val itemView = createMenuItemView(item)
            menuView?.addView(itemView)
        }
    }

    private fun createMenuItemView(item: MenuItem): View {
        val itemView = LayoutInflater.from(context).inflate(R.layout.context_menu_item, null)
        
        val titleText = itemView.findViewById<TextView>(R.id.menuItemTitle)
        val iconView = itemView.findViewById<View>(R.id.menuItemIcon)
        
        titleText.text = item.title
        iconView.background = ContextCompat.getDrawable(context, item.iconRes)
        
        itemView.setOnClickListener {
            onMenuItemClickListener?.invoke(item.action)
            hide()
        }
        
        return itemView
    }

    private fun createLayoutParams() {
        layoutParams = WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                @Suppress("DEPRECATION")
                WindowManager.LayoutParams.TYPE_PHONE
            },
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.CENTER
        }
    }

    /**
     * Show the context menu at specified position
     */
    fun show(x: Float = 0f, y: Float = 0f) {
        if (isShown || menuView == null) return

        try {
            // Position the menu
            layoutParams?.let { params ->
                params.x = x.toInt()
                params.y = y.toInt()
            }

            windowManager.addView(menuView, layoutParams)
            isShown = true
            
            // Animate entrance
            animateEntrance()
            
            // Schedule auto-hide
            scheduleAutoHide()
            
            Log.d(TAG, "Context menu shown at ($x, $y)")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error showing context menu", e)
        }
    }

    /**
     * Hide the context menu
     */
    fun hide() {
        if (!isShown || menuView == null) return

        try {
            // Cancel auto-hide
            cancelAutoHide()
            
            // Animate exit
            animateExit {
                try {
                    windowManager.removeView(menuView)
                    isShown = false
                    Log.d(TAG, "Context menu hidden")
                } catch (e: Exception) {
                    Log.e(TAG, "Error removing context menu", e)
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error hiding context menu", e)
        }
    }

    /**
     * Animate menu entrance
     */
    private fun animateEntrance() {
        menuView?.let { view ->
            view.alpha = 0f
            view.scaleX = 0.8f
            view.scaleY = 0.8f

            val fadeIn = ObjectAnimator.ofFloat(view, "alpha", 0f, 1f)
            val scaleX = ObjectAnimator.ofFloat(view, "scaleX", 0.8f, 1f)
            val scaleY = ObjectAnimator.ofFloat(view, "scaleY", 0.8f, 1f)

            AnimatorSet().apply {
                playTogether(fadeIn, scaleX, scaleY)
                duration = ANIMATION_DURATION
                interpolator = AccelerateDecelerateInterpolator()
                start()
            }
        }
    }

    /**
     * Animate menu exit
     */
    private fun animateExit(onComplete: () -> Unit) {
        menuView?.let { view ->
            val fadeOut = ObjectAnimator.ofFloat(view, "alpha", 1f, 0f)
            val scaleX = ObjectAnimator.ofFloat(view, "scaleX", 1f, 0.8f)
            val scaleY = ObjectAnimator.ofFloat(view, "scaleY", 1f, 0.8f)

            AnimatorSet().apply {
                playTogether(fadeOut, scaleX, scaleY)
                duration = ANIMATION_DURATION / 2
                interpolator = AccelerateDecelerateInterpolator()
                start()
            }.doOnEnd {
                onComplete()
            }
        }
    }

    /**
     * Schedule auto-hide after delay
     */
    private fun scheduleAutoHide() {
        cancelAutoHide()
        autoHideRunnable = Runnable { hide() }
        android.os.Handler(android.os.Looper.getMainLooper())
            .postDelayed(autoHideRunnable!!, AUTO_HIDE_DELAY)
    }

    /**
     * Cancel auto-hide
     */
    private fun cancelAutoHide() {
        autoHideRunnable?.let { runnable ->
            android.os.Handler(android.os.Looper.getMainLooper()).removeCallbacks(runnable)
            autoHideRunnable = null
        }
    }

    /**
     * Set menu item click listener
     */
    fun setOnMenuItemClickListener(listener: (GestureManager.GestureType) -> Unit) {
        onMenuItemClickListener = listener
    }

    /**
     * Check if menu is currently shown
     */
    fun isVisible(): Boolean = isShown

    /**
     * Toggle menu visibility
     */
    fun toggle(x: Float = 0f, y: Float = 0f) {
        if (isShown) {
            hide()
        } else {
            show(x, y)
        }
    }
}

// Extension function for AnimatorSet
private fun AnimatorSet.doOnEnd(action: () -> Unit) {
    addListener(object : android.animation.Animator.AnimatorListener {
        override fun onAnimationStart(animation: android.animation.Animator) {}
        override fun onAnimationEnd(animation: android.animation.Animator) { action() }
        override fun onAnimationCancel(animation: android.animation.Animator) {}
        override fun onAnimationRepeat(animation: android.animation.Animator) {}
    })
}
