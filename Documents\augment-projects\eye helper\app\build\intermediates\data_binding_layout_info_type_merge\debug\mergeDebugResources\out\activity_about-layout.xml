<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_about" modulePackage="com.motasem.visualpointer" filePath="app\src\main\res\layout\activity_about.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_about_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="348" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="11" startOffset="4" endLine="16" endOffset="70"/></Target><Target id="@+id/appVersionText" view="TextView"><Expressions/><location startLine="46" startOffset="12" endLine="53" endOffset="41"/></Target><Target id="@+id/whatsappContactCard" view="LinearLayout"><Expressions/><location startLine="109" startOffset="12" endLine="151" endOffset="26"/></Target><Target id="@+id/emailContactCard" view="LinearLayout"><Expressions/><location startLine="154" startOffset="12" endLine="196" endOffset="26"/></Target><Target id="@+id/githubLinkCard" view="LinearLayout"><Expressions/><location startLine="210" startOffset="12" endLine="238" endOffset="26"/></Target><Target id="@+id/rateAppCard" view="LinearLayout"><Expressions/><location startLine="241" startOffset="12" endLine="269" endOffset="26"/></Target><Target id="@+id/shareAppCard" view="LinearLayout"><Expressions/><location startLine="272" startOffset="12" endLine="300" endOffset="26"/></Target><Target id="@+id/privacyPolicyCard" view="LinearLayout"><Expressions/><location startLine="303" startOffset="12" endLine="331" endOffset="26"/></Target></Targets></Layout>