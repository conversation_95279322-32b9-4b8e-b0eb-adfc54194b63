// Generated by view binder compiler. Do not edit!
package com.motasem.visualpointer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.camera.view.PreviewView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.motasem.visualpointer.R;
import com.motasem.visualpointer.ui.GraphicOverlay;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityCalibrationBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final Button backFromCalibrationButton;

  @NonNull
  public final GraphicOverlay calibrationGraphicOverlay;

  @NonNull
  public final TextView calibrationInstruction;

  @NonNull
  public final View calibrationPoint;

  @NonNull
  public final FrameLayout calibrationPointsContainer;

  @NonNull
  public final PreviewView calibrationPreviewView;

  @NonNull
  public final TextView calibrationProgress;

  @NonNull
  public final ProgressBar calibrationProgressBar;

  @NonNull
  public final TextView calibrationTitle;

  @NonNull
  public final LinearLayout controlButtonsContainer;

  @NonNull
  public final LinearLayout instructionsContainer;

  @NonNull
  public final Button skipCalibrationButton;

  @NonNull
  public final Button startCalibrationButton;

  private ActivityCalibrationBinding(@NonNull FrameLayout rootView,
      @NonNull Button backFromCalibrationButton, @NonNull GraphicOverlay calibrationGraphicOverlay,
      @NonNull TextView calibrationInstruction, @NonNull View calibrationPoint,
      @NonNull FrameLayout calibrationPointsContainer, @NonNull PreviewView calibrationPreviewView,
      @NonNull TextView calibrationProgress, @NonNull ProgressBar calibrationProgressBar,
      @NonNull TextView calibrationTitle, @NonNull LinearLayout controlButtonsContainer,
      @NonNull LinearLayout instructionsContainer, @NonNull Button skipCalibrationButton,
      @NonNull Button startCalibrationButton) {
    this.rootView = rootView;
    this.backFromCalibrationButton = backFromCalibrationButton;
    this.calibrationGraphicOverlay = calibrationGraphicOverlay;
    this.calibrationInstruction = calibrationInstruction;
    this.calibrationPoint = calibrationPoint;
    this.calibrationPointsContainer = calibrationPointsContainer;
    this.calibrationPreviewView = calibrationPreviewView;
    this.calibrationProgress = calibrationProgress;
    this.calibrationProgressBar = calibrationProgressBar;
    this.calibrationTitle = calibrationTitle;
    this.controlButtonsContainer = controlButtonsContainer;
    this.instructionsContainer = instructionsContainer;
    this.skipCalibrationButton = skipCalibrationButton;
    this.startCalibrationButton = startCalibrationButton;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityCalibrationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityCalibrationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_calibration, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityCalibrationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.backFromCalibrationButton;
      Button backFromCalibrationButton = ViewBindings.findChildViewById(rootView, id);
      if (backFromCalibrationButton == null) {
        break missingId;
      }

      id = R.id.calibrationGraphicOverlay;
      GraphicOverlay calibrationGraphicOverlay = ViewBindings.findChildViewById(rootView, id);
      if (calibrationGraphicOverlay == null) {
        break missingId;
      }

      id = R.id.calibrationInstruction;
      TextView calibrationInstruction = ViewBindings.findChildViewById(rootView, id);
      if (calibrationInstruction == null) {
        break missingId;
      }

      id = R.id.calibrationPoint;
      View calibrationPoint = ViewBindings.findChildViewById(rootView, id);
      if (calibrationPoint == null) {
        break missingId;
      }

      id = R.id.calibrationPointsContainer;
      FrameLayout calibrationPointsContainer = ViewBindings.findChildViewById(rootView, id);
      if (calibrationPointsContainer == null) {
        break missingId;
      }

      id = R.id.calibrationPreviewView;
      PreviewView calibrationPreviewView = ViewBindings.findChildViewById(rootView, id);
      if (calibrationPreviewView == null) {
        break missingId;
      }

      id = R.id.calibrationProgress;
      TextView calibrationProgress = ViewBindings.findChildViewById(rootView, id);
      if (calibrationProgress == null) {
        break missingId;
      }

      id = R.id.calibrationProgressBar;
      ProgressBar calibrationProgressBar = ViewBindings.findChildViewById(rootView, id);
      if (calibrationProgressBar == null) {
        break missingId;
      }

      id = R.id.calibrationTitle;
      TextView calibrationTitle = ViewBindings.findChildViewById(rootView, id);
      if (calibrationTitle == null) {
        break missingId;
      }

      id = R.id.controlButtonsContainer;
      LinearLayout controlButtonsContainer = ViewBindings.findChildViewById(rootView, id);
      if (controlButtonsContainer == null) {
        break missingId;
      }

      id = R.id.instructionsContainer;
      LinearLayout instructionsContainer = ViewBindings.findChildViewById(rootView, id);
      if (instructionsContainer == null) {
        break missingId;
      }

      id = R.id.skipCalibrationButton;
      Button skipCalibrationButton = ViewBindings.findChildViewById(rootView, id);
      if (skipCalibrationButton == null) {
        break missingId;
      }

      id = R.id.startCalibrationButton;
      Button startCalibrationButton = ViewBindings.findChildViewById(rootView, id);
      if (startCalibrationButton == null) {
        break missingId;
      }

      return new ActivityCalibrationBinding((FrameLayout) rootView, backFromCalibrationButton,
          calibrationGraphicOverlay, calibrationInstruction, calibrationPoint,
          calibrationPointsContainer, calibrationPreviewView, calibrationProgress,
          calibrationProgressBar, calibrationTitle, controlButtonsContainer, instructionsContainer,
          skipCalibrationButton, startCalibrationButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
