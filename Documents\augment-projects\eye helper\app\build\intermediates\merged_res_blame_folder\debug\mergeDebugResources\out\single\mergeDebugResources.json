[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\xml_accessibility_service_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\xml\\accessibility_service_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\mipmap-xxxhdpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\mipmap-xxxhdpi\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\mipmap-xxhdpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\mipmap-xxhdpi\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\drawable_splash_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\drawable\\splash_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\drawable_ic_home.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\drawable\\ic_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\layout_activity_calibration.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\layout\\activity_calibration.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\drawable_status_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\drawable\\status_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\drawable_ic_back.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\drawable\\ic_back.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\layout_activity_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\layout\\activity_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\mipmap-mdpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\mipmap-mdpi\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\drawable_ic_eye.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\drawable\\ic_eye.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\layout_activity_about.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\layout\\activity_about.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\drawable_ic_click.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\drawable\\ic_click.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\mipmap-hdpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\mipmap-hdpi\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\drawable_cursor_pulse_ring.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\drawable\\cursor_pulse_ring.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\drawable_context_menu_item_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\drawable\\context_menu_item_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\layout_overlay_cursor.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\layout\\overlay_cursor.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\mipmap-xxhdpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\mipmap-xxhdpi\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\drawable_context_menu_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\drawable\\context_menu_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\drawable_ic_scroll_up.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\drawable\\ic_scroll_up.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\drawable_calibration_point.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\drawable\\calibration_point.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\mipmap-xhdpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\mipmap-xhdpi\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\mipmap-hdpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\mipmap-hdpi\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\mipmap-xhdpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\mipmap-xhdpi\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\drawable_ic_long_click.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\drawable\\ic_long_click.xml"}, {"merged": "com.motasem.visualpointer.app-debug-43:/layout_activity_about.xml.flat", "source": "com.motasem.visualpointer.app-main-45:/layout/activity_about.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\drawable_cursor_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\drawable\\cursor_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\mipmap-xxxhdpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\mipmap-xxxhdpi\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\layout_context_menu_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\layout\\context_menu_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\drawable_ic_recent.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\drawable\\ic_recent.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\mipmap-mdpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\mipmap-mdpi\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\drawable_ic_launcher_simple.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\drawable\\ic_launcher_simple.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-debug-43:\\drawable_ic_scroll_down.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\drawable\\ic_scroll_down.xml"}]