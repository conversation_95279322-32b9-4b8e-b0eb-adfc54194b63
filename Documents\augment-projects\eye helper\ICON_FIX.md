# حل مشكلة الأيقونات - المؤشر البصري

## إذا استمر خطأ الأيقونات، جرب هذه الحلول:

### الحل الأول: استخدام أيقونة بديلة بسيطة

قم بتعديل AndroidManifest.xml واستبدل:
```xml
android:icon="@mipmap/ic_launcher"
android:roundIcon="@mipmap/ic_launcher_round"
```

بـ:
```xml
android:icon="@drawable/ic_launcher_simple"
android:roundIcon="@drawable/ic_launcher_simple"
```

### الحل الثاني: إنشاء أيقونات PNG بسيطة

إذا لم تعمل الأيقونات XML، أنشئ ملفات PNG:

1. اذهب إلى: https://romannurik.github.io/AndroidAssetStudio/icons-launcher.html
2. ارفع أي صورة عين أو استخدم النص "VP"
3. حمل الملفات المضغوطة
4. انسخ الملفات إلى مجلدات res/mipmap-*

### الحل الثالث: استخدام أيقونة النظام

استبدل في AndroidManifest.xml:
```xml
android:icon="@android:drawable/ic_menu_camera"
android:roundIcon="@android:drawable/ic_menu_camera"
```

### الحل الرابع: إنشاء أيقونة بسيطة جداً

أنشئ ملف res/drawable/simple_icon.xml:
```xml
<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="oval">
    <solid android:color="#2196F3" />
    <size android:width="48dp" android:height="48dp" />
</shape>
```

ثم استخدمه في AndroidManifest.xml:
```xml
android:icon="@drawable/simple_icon"
android:roundIcon="@drawable/simple_icon"
```

## خطوات سريعة للحل:

1. جرب بناء المشروع الآن (قد يعمل بعد إضافة الملفات)
2. إذا لم يعمل، استخدم الحل الثالث (أيقونة النظام)
3. إذا لم يعمل، استخدم الحل الرابع (الشكل البسيط)

## أمر البناء:
```bash
./gradlew clean
./gradlew assembleDebug
```

أو في Windows:
```cmd
gradlew.bat clean
gradlew.bat assembleDebug
```
