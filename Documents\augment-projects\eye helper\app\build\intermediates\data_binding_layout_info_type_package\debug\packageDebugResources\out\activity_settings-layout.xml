<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_settings" modulePackage="com.motasem.visualpointer" filePath="app\src\main\res\layout\activity_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_settings_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="198" endOffset="12"/></Target><Target id="@+id/eyeTrackingSensitivitySeekBar" view="SeekBar"><Expressions/><location startLine="48" startOffset="16" endLine="54" endOffset="43"/></Target><Target id="@+id/blinkSensitivitySeekBar" view="SeekBar"><Expressions/><location startLine="64" startOffset="16" endLine="70" endOffset="43"/></Target><Target id="@+id/cursorSpeedSeekBar" view="SeekBar"><Expressions/><location startLine="80" startOffset="16" endLine="86" endOffset="43"/></Target><Target id="@+id/blinkDurationSeekBar" view="SeekBar"><Expressions/><location startLine="96" startOffset="16" endLine="102" endOffset="43"/></Target><Target id="@+id/cursorSizeSeekBar" view="SeekBar"><Expressions/><location startLine="135" startOffset="16" endLine="141" endOffset="43"/></Target><Target id="@+id/showFaceOutlineSwitch" view="Switch"><Expressions/><location startLine="157" startOffset="20" endLine="161" endOffset="48"/></Target><Target id="@+id/saveSettingsButton" view="Button"><Expressions/><location startLine="176" startOffset="12" endLine="180" endOffset="46"/></Target><Target id="@+id/resetSettingsButton" view="Button"><Expressions/><location startLine="182" startOffset="12" endLine="186" endOffset="44"/></Target><Target id="@+id/backButton" view="Button"><Expressions/><location startLine="188" startOffset="12" endLine="192" endOffset="37"/></Target></Targets></Layout>