[{"merged": "com.motasem.visualpointer.app-mergeDebugResources-42:/layout/activity_about.xml", "source": "com.motasem.visualpointer.app-main-45:/layout/activity_about.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-mergeDebugResources-42:\\layout\\activity_about.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\layout\\activity_about.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-mergeDebugResources-42:\\layout\\activity_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-mergeDebugResources-42:\\layout\\activity_settings.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\layout\\activity_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-mergeDebugResources-42:\\layout\\overlay_cursor.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\layout\\overlay_cursor.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-mergeDebugResources-42:\\layout\\context_menu_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\layout\\context_menu_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-mergeDebugResources-42:\\layout\\activity_calibration.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.motasem.visualpointer.app-main-45:\\layout\\activity_calibration.xml"}]