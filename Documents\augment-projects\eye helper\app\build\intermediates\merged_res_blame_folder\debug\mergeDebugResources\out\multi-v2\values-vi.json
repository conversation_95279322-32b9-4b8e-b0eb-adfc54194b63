{"logs": [{"outputFile": "com.motasem.visualpointer.app-mergeDebugResources-41:/values-vi/values-vi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\eb17ecbf9872120b924d2bbfd7cfe0e6\\transformed\\core-1.12.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "38,39,40,41,42,43,44,127", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3402,3499,3601,3700,3800,3903,4016,11422", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "3494,3596,3695,3795,3898,4011,4127,11518"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9f08e0d719849b14ed4ab8905bd80cf7\\transformed\\play-services-base-18.1.0\\res\\values-vi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,449,570,675,836,962,1077,1177,1346,1449,1602,1728,1883,2028,2092,2152", "endColumns": "97,157,120,104,160,125,114,99,168,102,152,125,154,144,63,59,78", "endOffsets": "290,448,569,674,835,961,1076,1176,1345,1448,1601,1727,1882,2027,2091,2151,2230"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4425,4527,4689,4814,4923,5088,5218,5337,5569,5742,5849,6006,6136,6295,6444,6512,6576", "endColumns": "101,161,124,108,164,129,118,103,172,106,156,129,158,148,67,63,82", "endOffsets": "4522,4684,4809,4918,5083,5213,5332,5436,5737,5844,6001,6131,6290,6439,6507,6571,6654"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ec7b75c409fae5c6e754c810938a9a33\\transformed\\appcompat-1.6.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,2798", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,422,524,633,717,820,939,1017,1093,1184,1277,1372,1466,1566,1659,1754,1848,1939,2030,2114,2218,2326,2427,2532,2647,2752,2909,11337", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "417,519,628,712,815,934,1012,1088,1179,1272,1367,1461,1561,1654,1749,1843,1934,2025,2109,2213,2321,2422,2527,2642,2747,2904,3003,11417"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\949294b35df779989b974dc81fe3e123\\transformed\\play-services-basement-18.1.0\\res\\values-vi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "123", "endOffsets": "318"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5441", "endColumns": "127", "endOffsets": "5564"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\06fd2fa39dd57ce426fb9e8f08397def\\transformed\\material-1.11.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,421,499,579,659,758,872,952,1015,1109,1183,1242,1328,1390,1451,1509,1573,1634,1688,1805,1862,1922,1976,2051,2178,2262,2340,2470,2554,2632,2766,2857,2938,2989,3040,3106,3174,3250,3331,3411,3490,3565,3638,3714,3820,3909,3986,4077,4171,4245,4315,4408,4457,4538,4604,4689,4775,4837,4901,4964,5035,5134,5239,5337,5442,5497,5552", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,78,76,77,79,79,98,113,79,62,93,73,58,85,61,60,57,63,60,53,116,56,59,53,74,126,83,77,129,83,77,133,90,80,50,50,65,67,75,80,79,78,74,72,75,105,88,76,90,93,73,69,92,48,80,65,84,85,61,63,62,70,98,104,97,104,54,54,77", "endOffsets": "260,339,416,494,574,654,753,867,947,1010,1104,1178,1237,1323,1385,1446,1504,1568,1629,1683,1800,1857,1917,1971,2046,2173,2257,2335,2465,2549,2627,2761,2852,2933,2984,3035,3101,3169,3245,3326,3406,3485,3560,3633,3709,3815,3904,3981,4072,4166,4240,4310,4403,4452,4533,4599,4684,4770,4832,4896,4959,5030,5129,5234,5332,5437,5492,5547,5625"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3008,3087,3164,3242,3322,4132,4231,4345,6659,6722,6816,6890,6949,7035,7097,7158,7216,7280,7341,7395,7512,7569,7629,7683,7758,7885,7969,8047,8177,8261,8339,8473,8564,8645,8696,8747,8813,8881,8957,9038,9118,9197,9272,9345,9421,9527,9616,9693,9784,9878,9952,10022,10115,10164,10245,10311,10396,10482,10544,10608,10671,10742,10841,10946,11044,11149,11204,11259", "endLines": "5,33,34,35,36,37,45,46,47,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "endColumns": "12,78,76,77,79,79,98,113,79,62,93,73,58,85,61,60,57,63,60,53,116,56,59,53,74,126,83,77,129,83,77,133,90,80,50,50,65,67,75,80,79,78,74,72,75,105,88,76,90,93,73,69,92,48,80,65,84,85,61,63,62,70,98,104,97,104,54,54,77", "endOffsets": "310,3082,3159,3237,3317,3397,4226,4340,4420,6717,6811,6885,6944,7030,7092,7153,7211,7275,7336,7390,7507,7564,7624,7678,7753,7880,7964,8042,8172,8256,8334,8468,8559,8640,8691,8742,8808,8876,8952,9033,9113,9192,9267,9340,9416,9522,9611,9688,9779,9873,9947,10017,10110,10159,10240,10306,10391,10477,10539,10603,10666,10737,10836,10941,11039,11144,11199,11254,11332"}}]}]}