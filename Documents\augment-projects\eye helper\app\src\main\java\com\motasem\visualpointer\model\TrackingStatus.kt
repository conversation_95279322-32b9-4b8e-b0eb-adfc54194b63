package com.motasem.visualpointer.model

/**
 * Enum class representing different eye tracking statuses
 */
enum class TrackingStatus {
    NO_FACE,
    MULTIPLE_FACES,
    FACE_DETECTED,
    LEFT_EYE_CLOSED,
    RIGHT_EYE_CLOSED,
    BOTH_EYES_CLOSED,
    TRACKING_ACTIVE,
    CALIBRATING,
    ERROR
}

/**
 * Data class for eye tracking data
 */
data class EyeTrackingData(
    val gazeX: Float = 0f,
    val gazeY: Float = 0f,
    val leftEyeOpenProbability: Float = 1f,
    val rightEyeOpenProbability: Float = 1f,
    val faceDetected: Boolean = false,
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * Data class for blink detection
 */
data class BlinkEvent(
    val eye: BlinkEye,
    val duration: Long,
    val timestamp: Long = System.currentTimeMillis()
)

enum class BlinkEye {
    LEFT,
    RIGHT,
    BOTH
}

/**
 * Data class for cursor position
 */
data class CursorPosition(
    val x: Float,
    val y: Float,
    val isVisible: Boolean = true
)
